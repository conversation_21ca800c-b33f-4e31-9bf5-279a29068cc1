<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="?attr/color_theme_dark_70">
    <item>
        <selector xmlns:android="http://schemas.android.com/apk/res/android">
            <item
                android:state_focused="true"
                android:state_pressed="true">
                <shape>
                    <corners android:radius="@dimen/vs_5" />
                    <solid android:color="?attr/color_theme_dark_70" />
                </shape>
            </item>
            <item
                android:state_focused="false"
                android:state_pressed="true">
                <shape>
                    <corners android:radius="@dimen/vs_5" />
                    <solid android:color="?attr/color_theme_dark_70" />
                </shape>
            </item>
            <item android:state_focused="true">
                <shape>
                    <corners android:radius="@dimen/vs_5" />
                    <solid android:color="?attr/color_theme_dark_70" />
	    <!--添加选中边框-->
	    <stroke android:width="2mm" android:color="@android:color/white" />
                </shape>
            </item>
            <item
                android:state_focused="false"
                android:state_pressed="false">
                <shape>
                    <corners android:radius="@dimen/vs_5" />
                    <solid android:color="@color/color_000000_60" />
                </shape>
            </item>
        </selector>
    </item>
</ripple>