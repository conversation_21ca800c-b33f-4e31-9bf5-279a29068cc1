<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_440"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="vertical"
        android:padding="@dimen/vs_30">


        <TextView
            android:id="@+id/tipInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:lineSpacingExtra="@dimen/ts_50"
            android:lineSpacingMultiplier="0"
            android:textAlignment="gravity"
            android:textColor="@color/color_000000_80"
            android:textSize="@dimen/ts_24"
            tools:text="1111111111111111111111111111111111111111111111111111" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_30"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/leftBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:gravity="center"
                android:padding="@dimen/vs_10"
                android:text="确定"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_22" />

            <TextView
                android:id="@+id/rightBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:gravity="center"
                android:padding="@dimen/vs_10"
                android:text="取消"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_22" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>