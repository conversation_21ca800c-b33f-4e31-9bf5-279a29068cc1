<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/vs_600"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_dialog_bg_main"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_20"
    android:paddingTop="@dimen/vs_10"
    android:paddingRight="@dimen/vs_20"
    android:paddingBottom="@dimen/vs_10">

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/vs_20"
                android:layout_marginRight="@dimen/vs_20"
                android:gravity="center_vertical|end"
                android:text="空间名称"
                android:textColor="@color/color_FF000000"
                android:textSize="@dimen/ts_20" />

            <EditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_weight="1"
                android:background="@drawable/shape_live_channel_num"
                android:hint="空间名称"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:textColor="@android:color/white"
                android:textColorHint="#99FFFFFF"
                android:textSize="@dimen/ts_20" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/vs_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/vs_20"
                android:layout_marginRight="@dimen/vs_20"
                android:gravity="center_vertical|end"
                android:text="Alist地址"
                android:textColor="@color/color_FF000000"
                android:textSize="@dimen/ts_20" />

            <EditText
                android:id="@+id/etUrl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_weight="1"
                android:background="@drawable/shape_live_channel_num"
                android:hint="Alist 网站地址"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:textColor="@android:color/white"
                android:textColorHint="#99FFFFFF"
                android:textSize="@dimen/ts_20" />
        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/vs_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/vs_20"
                android:layout_marginRight="@dimen/vs_20"
                android:gravity="center_vertical|end"
                android:text="初始路径"
                android:textColor="@color/color_FF000000"
                android:textSize="@dimen/ts_20" />

            <EditText
                android:id="@+id/etInitPath"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_weight="1"
                android:background="@drawable/shape_live_channel_num"
                android:hint="初始路径"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:textColor="@android:color/white"
                android:textColorHint="#99FFFFFF"
                android:textSize="@dimen/ts_20" />
        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/vs_10">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/vs_20"
                android:layout_marginRight="@dimen/vs_20"
                android:gravity="center_vertical|end"
                android:text="密码"
                android:textColor="@color/color_FF000000"
                android:textSize="@dimen/ts_20" />

            <EditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_weight="1"
                android:background="@drawable/shape_live_channel_num"
                android:hint="密码"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:textColor="@android:color/white"
                android:textColorHint="#99FFFFFF"
                android:textSize="@dimen/ts_20" />
        </TableRow>
    </TableLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/vs_10"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btnConfirm"
            android:layout_width="@dimen/vs_120"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/vs_100"
            android:layout_marginRight="@dimen/vs_100"
            android:background="@drawable/button_dialog_main"
            android:focusable="true"
            android:gravity="center"
            android:padding="@dimen/vs_10"
            android:text="确定"
            android:textColor="@android:color/white"
            android:textSize="@dimen/vs_20" />

        <TextView
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/vs_120"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_main"
            android:focusable="true"
            android:gravity="center"
            android:padding="@dimen/vs_10"
            android:text="取消"
            android:textColor="@android:color/white"
            android:textSize="@dimen/vs_20" />

    </LinearLayout>

</LinearLayout>