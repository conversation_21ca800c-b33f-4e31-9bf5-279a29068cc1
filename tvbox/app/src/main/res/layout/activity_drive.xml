<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_10"
    android:paddingRight="@dimen/vs_10">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/vs_10"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_50"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_50"
        android:paddingBottom="@dimen/vs_20">

        <View
            android:layout_width="@dimen/vs_5"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_8"
            android:layout_marginEnd="@dimen/vs_12"
            android:layout_marginRight="@dimen/vs_12"
            android:layout_marginBottom="@dimen/vs_8"
            android:background="?attr/color_theme" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_50"
            android:drawablePadding="@dimen/vs_10"
            android:ellipsize="start"
            android:maxWidth="@dimen/vs_960"
            android:singleLine="true"
            android:text="@string/act_drive"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_34"
            android:textStyle="bold" />

        <Space
            android:layout_width="@dimen/vs_0"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/btnHome"
            android:layout_width="@dimen/vs_60"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/vs_2"
            android:layout_marginLeft="@dimen/vs_2"
            android:layout_marginEnd="@dimen/vs_2"
            android:layout_marginRight="@dimen/vs_2"
            android:background="@drawable/shape_user_home"
            android:paddingLeft="@dimen/vs_5"
            android:paddingRight="@dimen/vs_5"
            app:srcCompat="@drawable/icon_home" />

        <ImageButton
            android:id="@+id/btnSort"
            android:layout_width="@dimen/vs_60"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/vs_2"
            android:layout_marginLeft="@dimen/vs_2"
            android:layout_marginEnd="@dimen/vs_2"
            android:layout_marginRight="@dimen/vs_2"
            android:background="@drawable/shape_user_home"
            android:paddingLeft="@dimen/vs_5"
            android:paddingRight="@dimen/vs_5"
            android:visibility="gone"
            app:srcCompat="@drawable/icon_sort" />

        <ImageButton
            android:id="@+id/btnAddServer"
            android:layout_width="@dimen/vs_60"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/vs_2"
            android:layout_marginLeft="@dimen/vs_2"
            android:layout_marginEnd="@dimen/vs_2"
            android:layout_marginRight="@dimen/vs_2"
            android:background="@drawable/shape_user_home"
            android:paddingLeft="@dimen/vs_5"
            android:paddingRight="@dimen/vs_5"
            app:srcCompat="@drawable/icon_circle_plus" />

        <ImageButton
            android:id="@+id/btnRemoveServer"
            android:layout_width="@dimen/vs_60"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/vs_2"
            android:layout_marginLeft="@dimen/vs_2"
            android:layout_marginEnd="@dimen/vs_2"
            android:layout_marginRight="@dimen/vs_2"
            android:background="@drawable/shape_user_home"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            app:srcCompat="@drawable/icon_trash_solid" />

    </LinearLayout>

    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/mGridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingLeft="@dimen/vs_30"
        android:paddingTop="@dimen/vs_10"
        android:paddingRight="@dimen/vs_30"
        android:paddingBottom="@dimen/vs_10"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
</LinearLayout>