<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="@dimen/vs_180"
        android:layout_height="match_parent"
        android:background="@color/color_32364E_40"
        android:orientation="vertical"
        android:paddingLeft="@dimen/vs_10"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_10"
        android:paddingBottom="@dimen/vs_20"
        android:visibility="gone">

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />
    </LinearLayout>

    <com.github.tvbox.osc.ui.tv.widget.NoScrollViewPager
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>