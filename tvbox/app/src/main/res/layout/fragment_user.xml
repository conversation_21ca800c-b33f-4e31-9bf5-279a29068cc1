<?xml version="1.0" encoding="utf-8"?>
<!-- 首页略缩图样式 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/vs_5"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_40"
    android:paddingTop="@dimen/vs_10"
    android:paddingRight="@dimen/vs_40"
    android:paddingBottom="@dimen/vs_20">

    <LinearLayout
        android:id="@+id/tvUserHome"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/tvHistory"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:nextFocusUp="@id/mGridViewCategory"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_history"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_history" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_hist"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvLive"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_live"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_live" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_live"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvSearch"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_search"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_search" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_search"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvFavorite"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_fav"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_fav" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_fav"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvPush"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_push"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_push" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_push"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvDrive"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_drive"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_folder" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_file"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tvSetting"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_100"
            android:layout_gravity="center"
            android:layout_margin="@dimen/vs_5"
            android:background="@drawable/shape_user_home"
            android:clickable="false"
            android:focusable="true"
            android:minWidth="@dimen/vs_150"
            android:orientation="horizontal"
            android:padding="@dimen/vs_15">

            <ImageView
                android:id="@+id/hm_settings"
                android:layout_width="@dimen/vs_40"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/vs_3"
                android:alpha="0.9"
                app:srcCompat="@drawable/hm_settings" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:fontFamily="sans-serif-black"
                android:gravity="center"
                android:padding="@dimen/vs_3"
                android:text="@string/hm_setting"
                android:textColor="@color/color_FFFFFF_90"
                android:textSize="@dimen/ts_24"
                android:textStyle="normal" />

        </LinearLayout>

    </LinearLayout>
<!-- 首页多行排版 -->
    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/tvHotListForGrid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvUserHome"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:descendantFocusability="afterDescendants"
        android:paddingLeft="@dimen/vs_20"
        android:paddingTop="@dimen/vs_30"
        android:paddingRight="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_30"
        android:tag="tvHotListForGrid"
        android:visibility="gone"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_20"
        app:tv_layoutManager="V7GridLayoutManager"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_20" />
<!-- 首页单行排版 -->
    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/tvHotListForLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_440"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_20"
        android:paddingTop="@dimen/vs_30"
        android:paddingRight="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_30"
        android:tag="tvHotListForLine"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_20"
        app:tv_layoutManager="V7LinearLayoutManager"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_20" />

</LinearLayout>