<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/vs_10"
    android:background="@drawable/shape_setting_sort_focus"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/vs_5"
        android:singleLine="true"
        android:textColor="@color/color_FFFFFF_80"
        android:textSize="@dimen/ts_24" />
</FrameLayout>