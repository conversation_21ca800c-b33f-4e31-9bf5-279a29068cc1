<?xml version="1.0" encoding="utf-8"?>
<!-- 首页顶部栏 -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/topLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_20"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_50"
        android:paddingRight="@dimen/vs_50"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="@dimen/vs_5"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_8"
            android:layout_marginEnd="@dimen/vs_12"
            android:layout_marginRight="@dimen/vs_12"
            android:layout_marginBottom="@dimen/vs_8"
            android:background="?attr/color_theme" />

        <TextView
            android:id="@+id/tvName"
            android:layout_width="@dimen/vs_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="false"
            android:gravity="left|center_vertical"
            android:nextFocusRight="@id/tvWifi"
            android:nextFocusDown="@id/mGridViewCategory"
            android:singleLine="true"
            android:text="@string/app_name"
            android:textAlignment="gravity"
            android:textColor="@color/font_home_selector"
            android:background="@drawable/shape_toplayout_tvname"
            android:textSize="@dimen/ts_30"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/tvWifi"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:layout_marginEnd="@dimen/vs_15"
            android:layout_marginRight="@dimen/vs_15"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:nextFocusLeft="@id/tvName"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_wifi_no" />

        <ImageView
            android:id="@+id/tvFind"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:layout_marginEnd="@dimen/vs_15"
            android:layout_marginRight="@dimen/vs_15"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_search" />

        <ImageView
            android:id="@+id/tvStyle"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:layout_marginEnd="@dimen/vs_15"
            android:layout_marginRight="@dimen/vs_15"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_10"
            android:src="@drawable/hm_left_right" />

        <ImageView
            android:id="@+id/tvDrawer"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:layout_marginEnd="@dimen/vs_15"
            android:layout_marginRight="@dimen/vs_15"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_drawer" />

        <ImageView
            android:id="@+id/tvMenu"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:layout_marginEnd="@dimen/vs_15"
            android:layout_marginRight="@dimen/vs_15"
            android:background="@drawable/button_dialog_vod"
            android:clickable="true"
            android:focusable="true"
            android:nextFocusDown="@id/tvDrive"
            android:padding="@dimen/vs_5"
            android:src="@drawable/hm_settings" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/vs_15"
            android:layout_marginLeft="@dimen/vs_15"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="right|center_vertical"
            android:textAlignment="gravity"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22"
            android:textStyle="bold"
            tools:text="DateTime" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="@dimen/vs_0"
        android:layout_height="@dimen/vs_0"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/topLayout">

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewCategory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginBottom="@dimen/vs_10"
            android:paddingLeft="@dimen/vs_50"
            android:paddingRight="@dimen/vs_50"
            app:tv_selectedItemIsCentered="true" />

        <com.github.tvbox.osc.ui.tv.widget.NoScrollViewPager
            android:id="@+id/mViewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>