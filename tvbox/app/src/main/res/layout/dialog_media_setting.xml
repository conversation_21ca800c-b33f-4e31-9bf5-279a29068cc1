<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_960"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg"
        android:minHeight="@dimen/vs_400"
        android:orientation="vertical"
        android:paddingLeft="@dimen/vs_30"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_30"
        android:paddingBottom="@dimen/vs_20">

        <TextView
            android:id="@+id/media_setting_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10"
            android:gravity="center"
            android:lineSpacingMultiplier="0"
            android:shadowColor="@color/color_FF000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5"
            android:singleLine="true"
            android:text="@string/dia_media_setting_title"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_26"
            android:textStyle="bold"
            tools:text="@string/dia_media_setting_title" />

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/list_media_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="@dimen/vs_260"
                android:orientation="vertical"
                app:layout_constrainedHeight="true"
                app:layout_constraintHeight_max="@dimen/vs_340"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/list_media_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/vs_20"
                android:orientation="vertical"
                app:layout_constrainedHeight="true"
                app:layout_constraintHeight_max="@dimen/vs_340"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

        </TableRow>
    </LinearLayout>
</FrameLayout>