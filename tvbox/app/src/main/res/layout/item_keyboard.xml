<?xml version="1.0" encoding="utf-8"?>
<!-- 搜索页拼音按钮 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/vs_50"
    android:layout_gravity="center"
    android:layout_margin="@dimen/vs_4"
    android:background="@drawable/shape_user_focus"
    android:focusable="true">

    <TextView
        android:id="@+id/keyName"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:textAlignment="gravity"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_22" />
</FrameLayout>