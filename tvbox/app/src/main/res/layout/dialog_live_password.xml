<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_400"
        android:layout_height="@dimen/vs_150"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="horizontal"
        android:padding="@dimen/vs_30">

        <EditText
            android:id="@+id/input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/input_dialog_api_input"
            android:hint="请输入密码"
            android:inputType="textPassword"
            android:maxLines="1"
            android:paddingLeft="2mm"
            android:paddingTop="@dimen/vs_10"
            android:paddingRight="@dimen/vs_2"
            android:paddingBottom="@dimen/vs_10"
            android:textColor="@color/color_000000_80"
            android:textColorHint="@color/color_3D3D3D_45"
            android:textSize="@dimen/ts_26" />

        <TextView
            android:id="@+id/inputSubmit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/vs_5"
            android:background="@drawable/button_dialog_main"
            android:focusable="true"
            android:gravity="center"
            android:padding="@dimen/vs_10"
            android:text="确定"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_22" />

    </LinearLayout>
</FrameLayout>