<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llListRoom"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/vs_5"
    android:layout_weight="1"
    android:background="@drawable/button_dialog_main"
    android:focusable="true"
    android:clickable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/vs_10">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_source"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_24" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_24" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/vs_10"
        android:layout_marginLeft="@dimen/vs_10"
        android:text=">"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_24" />
</LinearLayout>
