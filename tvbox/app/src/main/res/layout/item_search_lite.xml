<?xml version="1.0" encoding="utf-8"?>
<!-- 搜索文字列表 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_user_focus"
    android:focusable="true">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="marquee" 
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/vs_40"
        android:layout_marginTop="@dimen/vs_10"
        android:layout_marginRight="@dimen/vs_40"
        android:layout_marginBottom="@dimen/vs_10"
        android:singleLine="true"
        android:textColor="@color/color_FFFFFF"
        android:textSize="@dimen/ts_20" />
</FrameLayout>