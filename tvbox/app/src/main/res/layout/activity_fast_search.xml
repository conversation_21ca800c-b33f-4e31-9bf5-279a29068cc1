<?xml version="1.0" encoding="utf-8"?>
<!-- 聚合搜索页面 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_10"
        android:layout_marginBottom="@dimen/vs_10"
        android:paddingLeft="20mm">

        <TextView
            android:id="@+id/mSearchTitle"
            android:layout_width="@dimen/vs_300"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:paddingTop="@dimen/vs_10"
            android:shadowColor="@color/color_FF000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5"
            android:text="搜索(0/0)"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22"
            android:textStyle="bold" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWordFenci"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/llWord"
            android:layout_width="@dimen/vs_300"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="20mm"
            android:paddingBottom="@dimen/vs_20">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewWord"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="@dimen/vs_20"
            android:paddingBottom="@dimen/vs_20"
            android:paddingRight="@dimen/vs_20">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                android:padding="@dimen/vs_40"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_30"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_30" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewFilter"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                android:paddingTop="@dimen/vs_40"
                android:paddingLeft="@dimen/vs_50"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_55" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>