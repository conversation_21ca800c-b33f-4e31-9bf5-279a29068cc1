<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/button_dialog_main"
    android:focusable="true">

    <TextView
        android:id="@+id/tvName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="middle"
        android:gravity="center_vertical"
        android:padding="@dimen/vs_10"
        android:singleLine="true"
        android:textColor="@color/color_FFFFFF"
        android:textSize="@dimen/ts_20" />
</FrameLayout>