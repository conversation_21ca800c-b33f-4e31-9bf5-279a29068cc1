<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10mm"
    android:layout_marginBottom="10mm"
    android:layout_gravity="center"
    android:background="@drawable/shape_live_focus"
    android:focusable="true"
    android:focusableInTouchMode="false"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvSettingItemName"
        android:layout_width="0mm"
        android:layout_height="60mm"
        android:layout_weight="1"
        android:ellipsize="marquee"
        android:gravity="center"
        android:marqueeRepeatLimit="marquee_forever"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="2"
        android:singleLine="true"
        android:textColor="@android:color/white"
        android:textSize="22mm" />
</LinearLayout>