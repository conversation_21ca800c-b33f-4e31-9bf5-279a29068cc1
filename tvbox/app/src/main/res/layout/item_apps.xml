<?xml version="1.0" encoding="utf-8"?>
<!-- APP软件管理页面 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_thumb_radius"
    android:focusable="true"
    android:padding="@dimen/vs_1">

    <FrameLayout
        android:layout_width="@dimen/vs_160"
        android:layout_height="@dimen/vs_160">

        <ImageView
            android:id="@+id/ivApps"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/vs_25"
            android:padding="@dimen/vs_25"
            android:scaleType="fitCenter" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:orientation="vertical"
            android:padding="@dimen/vs_1">

            <TextView
                android:id="@+id/appName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_thumb_bottom_name"
                android:ellipsize="marquee"
                android:gravity="center"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingLeft="@dimen/vs_10"
                android:paddingTop="@dimen/vs_5"
                android:paddingRight="@dimen/vs_10"
                android:paddingBottom="@dimen/vs_5"
                android:singleLine="true"
                android:text="App Name"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_18" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/delFrameLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_user_delete"
            android:layout_gravity="center"
            android:visibility="gone">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="@dimen/vs_60"
                android:layout_height="@dimen/vs_60"
                android:layout_gravity="center"
                android:src="@drawable/icon_delete" />
        </FrameLayout>

    </FrameLayout>

</FrameLayout>