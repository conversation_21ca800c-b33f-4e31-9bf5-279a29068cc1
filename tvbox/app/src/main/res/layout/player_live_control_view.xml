<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/dialog_volume"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_margin="@dimen/vs_50"
        android:background="@drawable/shape_user_pause"
        android:gravity="center"
        android:orientation="vertical"
        android:tag="dialog_volume"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/volume_progressbar"
            style="@style/video_vertical_progressBar"
            android:layout_width="5dp"
            android:layout_height="90dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:max="100"
            android:tag="progressbar_volume" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="20dp"
            android:alpha="0.9"
            android:src="@drawable/play_volume" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/dialog_bright"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_gravity="right|center_vertical"
        android:layout_margin="@dimen/vs_50"
        android:background="@drawable/shape_user_pause"
        android:gravity="center"
        android:orientation="vertical"
        android:tag="dialog_brightness"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/brightness_progressbar"
            style="@style/video_vertical_progressBar"
            android:layout_width="5dp"
            android:layout_height="90dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:max="100"
            android:tag="progressbar_brightness" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="20dp"
            android:alpha="0.9"
            android:src="@drawable/play_brightness" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/top_container_hide"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_120"
        android:layout_gravity="top"
        android:gravity="end"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_20"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_20"
        android:tag="top_container_hide">

        <LinearLayout
            android:id="@+id/tv_top_line1_hide"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_title_top_hide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|left"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="left"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_50"
                android:shadowColor="@color/color_FF000000"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="3"
                android:tag="tv_title_top_hide"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24"
                android:textStyle="bold"
                android:visibility="visible" />

            <LinearLayout
                android:id="@+id/tv_speed_top_hide"
                android:layout_width="@dimen/vs_120"
                android:layout_height="@dimen/vs_40"
                android:orientation="horizontal"
                android:tag="tv_speed_top">

                <TextView
                    android:id="@+id/tv_play_speed_top_hide"
                    android:layout_width="@dimen/vs_70"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="right"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:tag="play_speed_top_hide"
                    android:text="Speed"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_MPBS_top_hide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|right"
                    android:layout_marginEnd="@dimen/vs_2"
                    android:layout_marginRight="@dimen/vs_2"
                    android:gravity="right"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:text="Mbps"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_16"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/tv_spin_top_hide"
                android:layout_width="@dimen/vs_45"
                android:layout_height="@dimen/vs_40"
                android:layout_gravity="top"
                android:layout_marginEnd="@dimen/vs_10"
                android:layout_marginRight="@dimen/vs_10"
                android:gravity="center|right"
                android:orientation="horizontal">

                <ProgressBar
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="@dimen/vs_10"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:indeterminateBehavior="repeat"
                    android:indeterminateDrawable="@drawable/dkplayer_progress_loading"
                    android:indeterminateOnly="true"
                    android:tag="vod_control_loading_hide" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/tv_pause_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="vod_control_pause"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/top_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_120"
            android:layout_gravity="top"
            android:gravity="end"
            android:orientation="vertical"
            android:paddingLeft="@dimen/vs_20"
            android:paddingTop="@dimen/vs_20"
            android:paddingRight="@dimen/vs_20"
            android:paddingBottom="@dimen/vs_20"
            android:tag="top_container">

            <LinearLayout
                android:id="@+id/tv_top_line1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="4dp"
                    android:layout_height="20dp"
                    android:layout_gravity="bottom|center"
                    android:layout_marginStart="@dimen/vs_50"
                    android:layout_marginLeft="@dimen/vs_50"
                    android:layout_marginEnd="@dimen/vs_8"
                    android:layout_marginRight="@dimen/vs_8" />

                <TextView
                    android:id="@+id/tv_title_top"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="left"
                    android:maxLines="1"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:tag="tv_title_top"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <LinearLayout
                    android:id="@+id/tv_speed_top"
                    android:layout_width="@dimen/vs_120"
                    android:layout_height="@dimen/vs_40"
                    android:orientation="horizontal"
                    android:tag="tv_speed_top">

                    <TextView
                        android:id="@+id/tv_play_speed_top"
                        android:layout_width="@dimen/vs_70"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:gravity="right"
                        android:shadowColor="@color/color_FF000000"
                        android:shadowDx="0"
                        android:shadowDy="0"
                        android:shadowRadius="3"
                        android:tag="play_speed_top"
                        android:text="Speed"
                        android:textColor="@color/color_FFFFFF"
                        android:textSize="@dimen/ts_22"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_MPBS_top"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom|right"
                        android:layout_marginEnd="@dimen/vs_2"
                        android:layout_marginRight="@dimen/vs_2"
                        android:gravity="right"
                        android:shadowColor="@color/color_FF000000"
                        android:shadowDx="0"
                        android:shadowDy="0"
                        android:shadowRadius="3"
                        android:text="Mbps"
                        android:textColor="@color/color_FFFFFF"
                        android:textSize="@dimen/ts_16"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/tv_spin_top"
                    android:layout_width="@dimen/vs_45"
                    android:layout_height="@dimen/vs_40"
                    android:layout_gravity="top"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:gravity="center|right"
                    android:orientation="horizontal">

                    <ProgressBar
                        android:layout_width="@dimen/vs_30"
                        android:layout_height="@dimen/vs_30"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="@dimen/vs_10"
                        android:layout_marginLeft="@dimen/vs_10"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:indeterminateBehavior="repeat"
                        android:indeterminateDrawable="@drawable/dkplayer_progress_loading"
                        android:indeterminateOnly="true"
                        android:tag="vod_control_loading" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tv_top_line2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="4dp"
                    android:layout_height="20dp"
                    android:layout_gravity="bottom|center"
                    android:layout_marginStart="@dimen/vs_50"
                    android:layout_marginLeft="@dimen/vs_50"
                    android:layout_marginEnd="@dimen/vs_8"
                    android:layout_marginRight="@dimen/vs_8" />

                <TextView
                    android:id="@+id/tv_resolution"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|left"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="left"
                    android:maxLines="1"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="3"
                    android:tag="tv_resolution"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold"
                    android:visibility="visible" />
            </LinearLayout>

        </LinearLayout>
        
        <TextView
            android:id="@+id/tv_play_load_net_speed_right_top"            
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="right"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingTop="@dimen/vs_5"
            android:paddingRight="@dimen/vs_2"
            android:tag="play_speed_topr"            
            android:text="Speed"
            android:textColor="?attr/color_theme"
            android:textStyle="bold"
            android:textSize="@dimen/ts_22" />

        <LinearLayout
            android:id="@+id/ll_pause"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_70"
            android:layout_gravity="right|top"
            android:layout_marginTop="@dimen/vs_80"
            android:background="@drawable/shape_user_pause_r"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center|right"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_40"
                android:layout_marginLeft="@dimen/vs_40"
                android:layout_marginEnd="@dimen/vs_10"
                android:layout_marginRight="@dimen/vs_10"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_pause_progress_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:shadowColor="@color/color_000000_60"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="5"
                    android:tag="vod_control_pause_t"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_26"
                    android:textStyle="bold"
                    tools:text="100" />

                <ProgressBar
                    android:id="@+id/video_pausebar"
                    style="@style/video_horizontal_progressBar"
                    android:layout_width="match_parent"
                    android:layout_height="3dp"
                    android:layout_marginTop="5dp"
                    android:layout_gravity="center|top"
                    android:max="100"
                    android:tag="pausebar_video" />
            </LinearLayout>

            <ImageView
                android:id="@+id/tv_pause_icon"
                android:layout_width="@dimen/vs_60"
                android:layout_height="@dimen/vs_60"
                android:layout_gravity="center"
                android:layout_marginEnd="@dimen/vs_30"
                android:layout_marginRight="@dimen/vs_30"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:src="@drawable/play_pause" />

        </LinearLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/tv_slide_progress_text"
        android:layout_width="@dimen/vs_200"
        android:layout_height="@dimen/vs_100"
        android:layout_gravity="center"
        android:background="@drawable/shape_user_pause"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:shadowColor="@color/color_000000_60"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="5"
        android:tag="vod_control_slide_info"
        android:textAlignment="gravity"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_26"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="100" />

    <LinearLayout
        android:id="@+id/tv_progress_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/vs_70"
        android:layout_gravity="right|top"
        android:layout_marginTop="@dimen/vs_80"
        android:background="@drawable/shape_user_pause_r"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center|right"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/vs_40"
            android:layout_marginLeft="@dimen/vs_40"
            android:layout_marginEnd="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_10"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:shadowColor="@color/color_000000_60"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="5"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_26"
                android:textStyle="bold"
                tools:text="100" />

            <ProgressBar
                android:id="@+id/video_progressbar"
                style="@style/video_horizontal_progressBar"
                android:layout_width="match_parent"
                android:layout_height="3dp"
                android:layout_marginTop="5dp"
                android:layout_gravity="center|top"
                android:max="100"
                android:tag="progressbar_video" />
        </LinearLayout>

        <ImageView
            android:id="@+id/tv_progress_icon"
            android:layout_width="@dimen/vs_60"
            android:layout_height="@dimen/vs_60"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/vs_30"
            android:layout_marginRight="@dimen/vs_30"
            android:focusable="false"
            android:focusableInTouchMode="false"
            tools:src="@drawable/play_rewind" />

    </LinearLayout>

</FrameLayout>