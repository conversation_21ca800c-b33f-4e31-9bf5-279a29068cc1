<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_720"
        android:layout_height="@dimen/vs_600"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_30"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="left"
            android:text="其他数据源中的相关资源, 没有找到可以尝试选词重搜"
            android:textColor="@color/color_000000_80"
            android:textSize="@dimen/ts_22" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWord"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginBottom="@dimen/vs_10"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

    </LinearLayout>
</FrameLayout>