<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_480"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="@dimen/vs_30"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_30"
        android:paddingBottom="@dimen/vs_30">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingMultiplier="0"
            android:shadowColor="@color/color_FF000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5"
            android:singleLine="true"
            android:text="@string/dia_hm_icon"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_26"
            android:textStyle="bold"
            tools:text="@string/dia_hm_icon" />

        <LinearLayout
            android:id="@+id/llSearch"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_5"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginTop="@dimen/vs_20"
            android:layout_marginEnd="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_main"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_20"
            android:paddingRight="@dimen/vs_20">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/hm_search"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvHomeSearch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_marginLeft="@dimen/vs_10"
                android:text=">"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llMenu"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_5"
            android:layout_marginLeft="@dimen/vs_5"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginEnd="@dimen/vs_5"
            android:layout_marginRight="@dimen/vs_5"
            android:layout_weight="1"
            android:background="@drawable/button_dialog_main"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_20"
            android:paddingRight="@dimen/vs_20">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/hm_setting"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvHomeMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_marginLeft="@dimen/vs_10"
                android:text=">"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>