<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_theme_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_dialog_bg_main">

    <LinearLayout
        android:layout_width="@dimen/vs_530"
        android:layout_height="wrap_content"
        android:padding="@dimen/vs_30"
        android:layout_gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/vs_10"
            android:singleLine="true"
            android:textColor="@color/color_FFFFFF"
            android:text="弹幕设置"
            android:textSize="@dimen/ts_26"
            android:textStyle="bold" />

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕开关"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/trv_onoff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/vs_15"
                android:layout_marginEnd="@dimen/vs_5"
                android:orientation="horizontal"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕颜色"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/trv_color"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/vs_15"
                android:layout_marginEnd="@dimen/vs_5"
                android:orientation="horizontal"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕速度"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/speed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/vs_15"
                android:layout_marginEnd="@dimen/vs_5"
                android:orientation="horizontal"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_layoutManager="V7LinearLayoutManager"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_10" />

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕大小"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <LinearLayout
                android:layout_width="@dimen/vs_0"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_weight="1"
                android:focusable="false"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/sizeSub"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewleft" />

                <TextView
                    android:id="@+id/size"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/vs_20"
                    android:layout_marginRight="@dimen/vs_20"
                    android:gravity="center_vertical|center"
                    android:text=""
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <ImageView
                    android:id="@+id/sizeAdd"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕行数"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <LinearLayout
                android:layout_width="@dimen/vs_0"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_weight="1"
                android:focusable="false"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/lineSub"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewleft" />

                <TextView
                    android:id="@+id/line"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/vs_20"
                    android:layout_marginRight="@dimen/vs_20"
                    android:gravity="center_vertical|center"
                    android:text=""
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <ImageView
                    android:id="@+id/lineAdd"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="@dimen/vs_5"
            android:paddingBottom="@dimen/vs_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="弹幕透明"
                android:textColor="@color/color_FFFFFF"
                android:gravity="center_vertical|left"
                android:textSize="@dimen/ts_20" />

            <LinearLayout
                android:layout_width="@dimen/vs_0"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_weight="1"
                android:focusable="false"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/alphaSub"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewleft" />

                <TextView
                    android:id="@+id/alpha"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/vs_20"
                    android:layout_marginRight="@dimen/vs_20"
                    android:gravity="center_vertical|center"
                    android:text=""
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <ImageView
                    android:id="@+id/alphaAdd"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_15"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
