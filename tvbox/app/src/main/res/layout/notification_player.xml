<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="130dp"
    android:padding="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
	<LinearLayout
		    android:layout_width="match_parent"
		    android:layout_height="match_parent"
			android:orientation="horizontal">
    
	    <ImageView
	        android:id="@+id/iv_logo"
	        android:layout_width="36dp"
	        android:layout_height="36dp"
	        android:layout_alignParentLeft="true"
	        android:src="@drawable/app_icon" />
	        
		<LinearLayout
		    android:layout_width="match_parent"
		    android:layout_height="match_parent"
		    android:layout_marginStart="5dp"
            android:layout_marginLeft="5dp"
			android:orientation="vertical">
		    <TextView
		        android:id="@+id/tv_title"
		        android:layout_width="wrap_content"
		        android:layout_height="wrap_content"
		        android:maxWidth="240dp"
		        android:singleLine="true"
		        android:ellipsize="marquee"
		        android:text="剧名"
		        android:textColor="@color/color_3D3D3D"
		        android:textStyle="bold"
		        android:textSize="12sp" />
		
		    <TextView
		        android:id="@+id/tv_subtitle"
		        android:layout_width="wrap_content"
		        android:layout_height="wrap_content"
		        android:maxWidth="240dp"
		        android:singleLine="true"
		        android:ellipsize="end"
		        android:layout_below="@id/tv_title"
		        android:text="SubtitleSubtitleSubtitleSubtitleSubtitleSubtitle"
		        android:layout_marginTop="5dp"
		        android:layout_marginBottom="10dp"/>    
		
			<LinearLayout
			    android:layout_below="@+id/tv_subtitle"
			    android:layout_width="match_parent"
			    android:layout_height="wrap_content"
			    android:gravity="center_vertical">
			
			    <ImageView
			        android:id="@+id/iv_previous"
			        android:layout_width="28dp"
			        android:layout_height="28dp"
			        android:padding="4dp"
			        android:src="@drawable/ic_notify_pre" />
			
			    <ImageView
			        android:id="@+id/iv_play_pause"
			        android:layout_width="28dp"
			        android:layout_height="28dp"
			        android:padding="4dp"
			        android:layout_marginHorizontal="20dp"
			        android:src="@drawable/ic_notify_pause" />
			
			    <ImageView
			        android:id="@+id/iv_next"
			        android:layout_width="28dp"
			        android:layout_height="28dp"
			        android:padding="4dp"
			        android:src="@drawable/ic_notify_next" />
			</LinearLayout>
		</LinearLayout>
	</LinearLayout>
</RelativeLayout>