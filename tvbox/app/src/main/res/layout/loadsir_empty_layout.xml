<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:layout_width="@dimen/vs_120"
            android:layout_height="@dimen/vs_120"
            android:src="@drawable/icon_empty" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_20"
            android:gravity="center"
            android:text="没找到数据"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_24" />
    </LinearLayout>

</FrameLayout>