<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/vs_460"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="horizontal"
        android:padding="@dimen/vs_30">

        <ImageView
            android:id="@+id/ivQRCode"
            android:layout_width="@dimen/vs_300"
            android:layout_height="@dimen/vs_300"
            android:layout_gravity="center"
            android:focusable="false" />

        <LinearLayout
            android:layout_width="@dimen/vs_640"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/vs_20"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_10"
                android:gravity="center"
                android:lineSpacingMultiplier="1.5"
                android:shadowColor="@color/color_000000_60"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="5"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_24"
                android:textStyle="bold"
                tools:text="1111111111111111111111111111111111111111111111111111" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:padding="@dimen/vs_5">

                    <LinearLayout
                        android:id="@+id/ll_apiHistory"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/vs_5"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/input_dialog_api_input"
                            android:hint="1. 请输入数据源地址"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/vs_2"
                            android:paddingTop="@dimen/vs_10"
                            android:paddingRight="@dimen/vs_2"
                            android:paddingBottom="@dimen/vs_10"
                            android:shadowColor="@color/color_000000_60"
                            android:shadowDx="0"
                            android:shadowDy="0"
                            android:shadowRadius="3"
                            android:textColor="@color/color_FFFFFF"
                            android:textColorHint="@color/color_FFFFFF_50"
                            android:textSize="@dimen/ts_22" />

                        <LinearLayout
                            android:id="@+id/apiHistory"
                            android:layout_width="@dimen/vs_80"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/vs_5"
                            android:layout_marginLeft="@dimen/vs_5"
                            android:background="@drawable/shape_setting_model_focus"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="@dimen/vs_10">

                            <ImageView
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:focusable="true"
                                android:src="@drawable/hm_history" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_liveHistory"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/vs_5"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/input_live"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/input_dialog_api_input"
                            android:hint="2. 请输入直播源地址 (optional)"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/vs_2"
                            android:paddingTop="@dimen/vs_10"
                            android:paddingRight="@dimen/vs_2"
                            android:paddingBottom="@dimen/vs_10"
                            android:shadowColor="@color/color_000000_60"
                            android:shadowDx="0"
                            android:shadowDy="0"
                            android:shadowRadius="3"
                            android:textColor="@color/color_FFFFFF"
                            android:textColorHint="@color/color_FFFFFF_50"
                            android:textSize="@dimen/ts_22" />

                        <LinearLayout
                            android:id="@+id/liveHistory"
                            android:layout_width="@dimen/vs_80"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/vs_5"
                            android:layout_marginLeft="@dimen/vs_5"
                            android:background="@drawable/shape_setting_model_focus"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="@dimen/vs_10">

                            <ImageView
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:focusable="true"
                                android:src="@drawable/hm_history" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_EPGHistory"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/vs_5"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/input_epg"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/input_dialog_api_input"
                            android:hint="3. 请输入直播EPG地址 (optional)"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/vs_2"
                            android:paddingTop="@dimen/vs_10"
                            android:paddingRight="@dimen/vs_2"
                            android:paddingBottom="@dimen/vs_10"
                            android:shadowColor="@color/color_000000_60"
                            android:shadowDx="0"
                            android:shadowDy="0"
                            android:shadowRadius="3"
                            android:textColor="@color/color_FFFFFF"
                            android:textColorHint="@color/color_FFFFFF_50"
                            android:textSize="@dimen/ts_22" />

                        <LinearLayout
                            android:id="@+id/EPGHistory"
                            android:layout_width="@dimen/vs_80"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/vs_5"
                            android:layout_marginLeft="@dimen/vs_5"
                            android:background="@drawable/shape_setting_model_focus"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:padding="@dimen/vs_10">

                            <ImageView
                                android:layout_width="@dimen/vs_30"
                                android:layout_height="@dimen/vs_30"
                                android:focusable="true"
                                android:src="@drawable/hm_history" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_proxy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="@dimen/vs_5"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/input_proxy"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/input_dialog_api_input"
                            android:hint="socks代理服务器IP:端口,……，exo播放失败时通过代理播放"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/vs_2"
                            android:paddingTop="@dimen/vs_10"
                            android:paddingRight="@dimen/vs_2"
                            android:paddingBottom="@dimen/vs_10"
                            android:shadowColor="@color/color_000000_60"
                            android:shadowDx="0"
                            android:shadowDy="0"
                            android:shadowRadius="3"
                            android:textColor="@color/color_FFFFFF"
                            android:textColorHint="@color/color_FFFFFF_50"
                            android:textSize="@dimen/ts_22" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/vs_10"
                android:padding="@dimen/vs_5"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/storagePermission"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="存储权限"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22" />

                <TextView
                    android:id="@+id/inputSubmit"
                    android:layout_width="@dimen/vs_80"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="确定"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</FrameLayout>