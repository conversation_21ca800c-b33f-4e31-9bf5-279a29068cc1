<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/llLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/topLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_20"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_50"
            android:paddingRight="@dimen/vs_50"
            android:paddingBottom="@dimen/vs_10">

            <ImageView
                android:id="@+id/ivThumb"
                android:layout_width="@dimen/vs_225"
                android:layout_height="@dimen/vs_300"
                android:clickable="true"
                android:focusable="false"
                android:scaleType="fitXY" />

            <View
                android:id="@+id/previewPlayerPlace"
                android:layout_width="@dimen/vs_530"
                android:layout_height="@dimen/vs_300"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_300"
                android:layout_marginStart="@dimen/vs_20"
                android:layout_marginLeft="@dimen/vs_20"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:ellipsize="end"
                    android:focusable="false"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_30"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvSite"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_FFFFFF_80"
                        android:textSize="@dimen/ts_18" />

                    <TextView
                        android:id="@+id/tvYear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_FFFFFF_80"
                        android:textSize="@dimen/ts_18" />

                    <TextView
                        android:id="@+id/tvArea"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_FFFFFF_80"
                        android:textSize="@dimen/ts_18" />

                    <TextView
                        android:id="@+id/tvLang"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_FFFFFF_80"
                        android:textSize="@dimen/ts_18" />

                    <TextView
                        android:id="@+id/tvType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_FFFFFF_80"
                        android:textSize="@dimen/ts_18" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvActor"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/color_FFFFFF_80"
                    android:textSize="@dimen/ts_18" />

                <TextView
                    android:id="@+id/tvDirector"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/color_FFFFFF_80"
                    android:textSize="@dimen/ts_18" />

                <TextView
                    android:id="@+id/tvDes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lineSpacingMultiplier="1.1"
                    android:maxLines="4"
                    android:textColor="@color/color_FFFFFF_80"
                    android:textSize="@dimen/ts_16" />

                <Space
                    android:layout_width="wrap_content"
                    android:layout_height="0mm"
                    android:layout_weight="1" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvPlay"
                        android:layout_width="@dimen/vs_100"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_play"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvQuickSearch"
                        android:layout_width="@dimen/vs_100"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_search"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvSort"
                        android:layout_width="@dimen/vs_100"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_sort"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvPush"
                        android:layout_width="@dimen/vs_100"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_push"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />
                        
                    <TextView
                        android:id="@+id/tvDesc"
                        android:layout_width="@dimen/vs_100"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_desc"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />    

                    <TextView
                        android:id="@+id/tvCollect"
                        android:layout_width="@dimen/vs_110"
                        android:layout_height="@dimen/vs_40"
                        android:background="@drawable/button_detail_all"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="@string/det_fav"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <ImageView
                        android:id="@+id/tvPlayUrl"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/vs_15"
                        android:layout_marginLeft="@dimen/vs_15"
                        android:focusable="false"
                        app:srcCompat="@drawable/button_detail_link" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:id="@+id/mEmptyPlaylist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            tools:visibility="gone">

            <ImageView
                android:layout_width="@dimen/vs_128"
                android:layout_height="@dimen/vs_128"
                android:layout_gravity="center"
                android:src="@drawable/icon_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_marginLeft="@dimen/vs_10"
                android:gravity="center"
                android:text="暂无播放数据"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />
        </LinearLayout>

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewFlag"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_45"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true" />
            
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mSeriesGroupView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_45"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true" />    

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:layout_marginBottom="@dimen/vs_30"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
    </LinearLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/previewPlayer"
        android:layout_width="@dimen/vs_530"
        android:layout_height="@dimen/vs_300"
        android:layout_marginStart="@dimen/vs_50"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_20"
        android:visibility="gone" />

    <View
        android:id="@+id/previewPlayerBlock"
        android:layout_width="@dimen/vs_530"
        android:layout_height="@dimen/vs_300"
        android:layout_marginStart="@dimen/vs_50"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_20"
        android:background="@drawable/button_detail_preview"
        android:focusable="true"
        android:visibility="gone" />
</FrameLayout>