<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_480"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/ts_40"
                android:padding="@dimen/vs_20"
                android:shadowColor="@color/color_FF000000"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="5"
                android:singleLine="true"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_26"
                android:textStyle="bold"
                tools:text="Subtitle Options" />

            <LinearLayout
                android:id="@+id/ll_subtitle_mode"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_25"
                android:layout_marginLeft="@dimen/vs_25"
                android:layout_marginEnd="@dimen/vs_25"
                android:layout_marginRight="@dimen/vs_25"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/selectInternal"
                    android:layout_width="@dimen/vs_128"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_marginEnd="@dimen/vs_5"
                    android:layout_marginRight="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="Internal"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <TextView
                    android:id="@+id/selectLocal"
                    android:layout_width="@dimen/vs_128"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_marginEnd="@dimen/vs_5"
                    android:layout_marginRight="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="Local"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <TextView
                    android:id="@+id/selectRemote"
                    android:layout_width="@dimen/vs_128"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_5"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:layout_marginEnd="@dimen/vs_5"
                    android:layout_marginRight="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="Remote"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_subtitle_size"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_55"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_25"
                android:layout_marginLeft="@dimen/vs_25"
                android:layout_marginTop="@dimen/vs_10"
                android:layout_marginEnd="@dimen/vs_25"
                android:layout_marginRight="@dimen/vs_25"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/vs_140"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="@string/sub_size"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleSizeMinus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewleft" />

                <TextView
                    android:id="@+id/subtitleSizeText"
                    android:layout_width="@dimen/vs_95"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_40"
                    android:layout_marginRight="@dimen/vs_40"
                    android:gravity="center"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="16"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleSizePlus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_subtitle_style"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_55"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_25"
                android:layout_marginLeft="@dimen/vs_25"
                android:layout_marginTop="@dimen/vs_10"
                android:layout_marginEnd="@dimen/vs_25"
                android:layout_marginRight="@dimen/vs_25"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/vs_140"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="@string/sub_style"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleStyleMinus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewleft" />

                <!--android:shadowColor="@color/color_FF000000"-->
                <com.github.tvbox.osc.subtitle.widget.SimpleSubtitleView
                    android:id="@+id/subtitleStyleText"
                    android:layout_width="@dimen/vs_95"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_40"
                    android:layout_marginRight="@dimen/vs_40"
                    android:gravity="center"
                    android:text="@string/sub_style_text"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleStylePlus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_subtitle_delay"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_55"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_25"
                android:layout_marginLeft="@dimen/vs_25"
                android:layout_marginEnd="@dimen/vs_25"
                android:layout_marginRight="@dimen/vs_25"
                android:layout_marginBottom="@dimen/vs_10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="@dimen/vs_140"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="@string/sub_delay"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_20"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleTimeMinus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewleft" />

                <TextView
                    android:id="@+id/subtitleTimeText"
                    android:layout_width="@dimen/vs_95"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_40"
                    android:layout_marginRight="@dimen/vs_40"
                    android:gravity="center"
                    android:shadowColor="@color/color_FF000000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="2"
                    android:text="16"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_24"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/subtitleTimePlus"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:background="@drawable/subtitle_circle"
                    android:focusable="true"
                    android:padding="@dimen/vs_8"
                    app:srcCompat="@drawable/scrollviewright" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
