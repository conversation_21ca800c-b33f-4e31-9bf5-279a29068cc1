<?xml version="1.0" encoding="utf-8"?>
<!-- 搜索略缩图页面 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="@dimen/vs_420"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_5"
            android:gravity="center_vertical"
            android:orientation="vertical">
            	
            <TextView
                android:id="@+id/filterBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_weight="1"
                android:background="@drawable/shape_user_focus"
                android:focusable="true"
                android:gravity="center"
                android:text="@string/global_search"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_marginTop="@dimen/vs_10"
                android:orientation="horizontal">
                	
                <com.github.tvbox.osc.ui.tv.widget.CustomEditText
                    android:id="@+id/etSearch"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/vs_50"
                    android:layout_marginEnd="@dimen/vs_10"
                    android:layout_marginRight="@dimen/vs_10"
                    android:layout_weight="1"
                    android:background="@drawable/input_search"
                    android:hint="@string/act_search_txt"
                    android:inputType="text"
                    android:singleLine="true"
                    android:imeOptions="actionDone"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_10"
                    android:paddingRight="@dimen/vs_10"
                    android:textColor="@android:color/white"
                    android:textColorHint="#70FFFFFF"
                    android:textSize="@dimen/ts_20" />

                <ImageView
                    android:id="@+id/tvSearchCheckbox"
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:layout_margin="@dimen/vs_2"
                    android:background="@drawable/shape_user_focus"
                    android:focusable="true"
                    android:padding="@dimen/vs_5"
                    android:src="@drawable/icon_filter" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_marginTop="@dimen/vs_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSearch"
                    android:layout_width="0mm"
                    android:layout_height="@dimen/vs_50"
                    android:layout_weight="1"
                    android:background="@drawable/shape_user_focus"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="@string/act_search"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20" />

                <Space
                    android:layout_width="@dimen/vs_10"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tvClear"
                    android:layout_width="0mm"
                    android:layout_height="@dimen/vs_50"
                    android:layout_weight="1"
                    android:background="@drawable/shape_user_focus"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="@string/act_search_clr"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20" />
            </LinearLayout>
        </LinearLayout>

        <com.github.tvbox.osc.ui.tv.widget.SearchKeyboard
            android:id="@+id/keyBoardRoot"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/remoteRoot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/vs_20"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.4"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <ImageView
                android:id="@+id/ivQRCode"
                android:layout_width="300mm"
                android:layout_height="300mm"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20mm" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llWord"
        android:layout_width="@dimen/vs_260"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_20">

        <TextView           
            android:id="@+id/mHotSearch_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/vs_10"
            android:text="@string/act_search_trend"
            android:textAlignment="viewStart"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22" />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWord"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">
        
        <RelativeLayout
            android:id="@+id/search_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_search_layout">

            <TextView
                android:textColor="@android:color/white"
                android:id="@+id/title_history"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_20"
                android:textSize="@dimen/ts_22"
                android:text="搜索历史" />

            <ImageView                
                android:id="@+id/clear_history"
                android:layout_width="@dimen/vs_50"
                android:layout_height="@dimen/vs_50"
                android:gravity="center"
                android:background="@drawable/shape_user_focus"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:padding="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_20"              
                android:src="@drawable/icon_delete"
                android:layout_alignParentRight="true" />
        </RelativeLayout>

        <com.yang.flowlayoutlibrary.FlowLayout
            android:id="@+id/tv_history"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/vs_20"
            android:layout_marginTop="@dimen/vs_20"
            android:layout_marginRight="@dimen/vs_20"
            app:backgroundResource="@drawable/shape_user_focus"
            app:horizontalSpacing="@dimen/vs_20"
            app:itemColor="@android:color/white"
            app:itemSize="@dimen/vs_20"
            app:textPaddingH="@dimen/vs_20"
            app:textPaddingV="@dimen/vs_10"
            app:verticalSpacing="@dimen/vs_20" />        

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:visibility="invisible"
            android:padding="@dimen/vs_25"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true" />
    </LinearLayout>
</LinearLayout>