<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="fontPath" format="string" />
    <attr name="textFieldStyle" format="reference" />

    <declare-styleable name="themeColor">
        <attr name="color_theme" format="color|reference" />
        <attr name="color_theme_80" format="color|reference" />
        <attr name="color_theme_70" format="color|reference" />
        <attr name="color_theme_60" format="color|reference" />
        <attr name="color_theme_50" format="color|reference" />
        <attr name="color_theme_40" format="color|reference" />
        <attr name="color_theme_dark_70" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="BaseVideoView">
        <attr name="looping" format="boolean"/>
        <attr name="enableAudioFocus" format="boolean"/>
        <attr name="screenScaleType" format="dimension">
            <enum name="type_default" value="0"/>
            <enum name="type_16_9" value="1"/>
            <enum name="type_4_3" value="2"/>
            <enum name="type_match_parent" value="3"/>
            <enum name="type_original" value="4"/>
            <enum name="type_center_crop" value="5"/>
        </attr>
        <attr name="playerBackgroundColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="lbBaseGridView">
        <attr name="focusOutFront" format="boolean" />
        <attr name="focusOutEnd" format="boolean" />
        <attr name="focusOutSideStart" format="boolean" />
        <attr name="focusOutSideEnd" format="boolean" />
        <attr name="horizontalMargin" format="dimension" />
        <attr name="verticalMargin" format="dimension" />
        <attr name="android:horizontalSpacing" />
        <attr name="android:verticalSpacing" />
        <attr name="android:gravity" />
    </declare-styleable>

    <declare-styleable name="lbVerticalGridView">
        <attr name="columnWidth" format="dimension">
            <enum name="wrap_content" value="-2" />
        </attr>
        <attr name="numberOfColumns" format="integer" />
    </declare-styleable>

    <declare-styleable name="lbHorizontalGridView">
        <attr name="rowHeight" format="dimension">
            <enum name="wrap_content" value="-2" />
        </attr>
        <attr name="numberOfRows" format="integer" />
    </declare-styleable>

    <declare-styleable name="RecyclerViewTV">
        <attr name="selectedItemOffsetStartTV" format="dimension" />
        <attr name="selectedItemOffsetEndTV" format="dimension" />
        <attr name="selectedItemIsCenteredTV" format="boolean" />
        <attr name="isMenuTV" format="boolean" />
        <attr name="isMemoryFocusTV" format="boolean" />
        <attr name="optimizeLayoutTV" format="boolean" />
        <attr name="verticalSpacingWithMarginsTV" format="dimension" />
        <attr name="horizontalSpacingWithMarginsTV" format="dimension" />
    </declare-styleable>

    <attr name="startColor" format="reference|color" />
    <attr name="topDelta" format="float" />
    <!-- Selected Paint Color (color). -->
    <attr name="endColor" format="reference|color" />

    <declare-styleable name="CardViewShadow">
        <!-- Default Paint Color (color). -->
        <attr name="startColor" />
        <!-- Selected Paint Color (color). -->
        <attr name="endColor" />
        <attr name="topDelta" />
    </declare-styleable>

</resources>