<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="?attr/color_theme" />
            <stroke android:width="2mm" android:color="@android:color/white" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="?attr/color_theme" />
        </shape>
    </item>
</selector>