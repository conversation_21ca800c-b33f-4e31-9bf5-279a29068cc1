<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true" android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="?attr/color_theme_70" />
        </shape>
    </item>
    <item android:state_focused="false" android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="?attr/color_theme_70" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="?attr/color_theme_70" />
	    <!--添加选中外边框-->
	    <stroke android:width="2mm" android:color="@android:color/white" />
        </shape>
    </item>
    <item android:state_focused="false" android:state_pressed="false">
        <shape>
            <corners android:radius="@dimen/vs_20" />
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>