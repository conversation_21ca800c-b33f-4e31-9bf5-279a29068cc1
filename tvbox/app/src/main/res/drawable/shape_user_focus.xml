<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <corners android:radius="@dimen/vs_14" />
            <stroke android:width="@dimen/vs_2" android:color="@color/color_FFFFFF" />
            <solid android:color="?attr/color_theme_40" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <corners android:radius="@dimen/vs_14" />
            <solid android:color="@color/color_3D3D3D_45"/>
        </shape>
    </item>
</selector>