# FreeTVPlayer 设计与技术文档

## 1. 项目概述

FreeTVPlayer是一款基于iOS/macOS平台的影视播放器应用，参考TVBox的功能设计，使用Swift和SwiftUI技术栈开发。项目目标是为苹果生态系统提供一个功能完整、性能优秀的免费影视播放解决方案。

## 2. 技术选型

### 2.1 开发语言和框架
- **开发语言**: Swift 5.9+
- **UI框架**: SwiftUI (iOS 15.0+, macOS 12.0+)
- **网络框架**: URLSession + Combine
- **数据库**: Core Data / SQLite
- **视频播放**: AVPlayer + AVKit
- **依赖管理**: Swift Package Manager

### 2.2 目标平台
- **iOS**: iPhone (iOS 15.0+)
- **macOS**: Mac (macOS 12.0+)
- **未来扩展**: iPadOS, tvOS, Apple TV

## 3. 架构设计

### 3.1 整体架构 (MVVM + Clean Architecture)
```
FreeTVPlayer/
├── Presentation/           # 表现层
│   ├── Views/             # SwiftUI视图
│   ├── ViewModels/        # 视图模型
│   └── Components/        # 可复用组件
├── Domain/                # 业务逻辑层
│   ├── Entities/          # 实体模型
│   ├── UseCases/          # 用例
│   └── Repositories/      # 仓库接口
├── Data/                  # 数据层
│   ├── Repositories/      # 仓库实现
│   ├── DataSources/       # 数据源
│   ├── Network/           # 网络服务
│   └── Local/             # 本地存储
├── Core/                  # 核心模块
│   ├── Extensions/        # 扩展
│   ├── Utils/             # 工具类
│   └── Constants/         # 常量
└── Resources/             # 资源文件
```

### 3.2 模块依赖关系
```
Presentation → Domain ← Data
     ↓           ↓       ↓
   Core ←――――――――――――――――――
```

## 4. 核心功能模块设计

### 4.1 数据源管理模块

#### 4.1.1 数据源配置
```swift
struct DataSource: Codable, Identifiable {
    let id = UUID()
    let key: String
    let name: String
    let api: String
    let type: DataSourceType
    let searchable: Bool
    let quickSearch: Bool
    let ext: String?
    let playerType: PlayerType?
}

enum DataSourceType: Int, Codable {
    case xml = 0
    case json = 1
    case spider = 3
}
```

#### 4.1.2 配置管理器
```swift
class DataSourceManager: ObservableObject {
    @Published var dataSources: [DataSource] = []
    @Published var currentSource: DataSource?
    
    func loadConfiguration(from url: String) async throws
    func addDataSource(_ source: DataSource)
    func removeDataSource(_ source: DataSource)
    func validateDataSource(_ source: DataSource) async -> Bool
}
```

### 4.2 视频播放模块

#### 4.2.1 播放器封装
```swift
class VideoPlayerManager: ObservableObject {
    @Published var player: AVPlayer?
    @Published var isPlaying: Bool = false
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0
    
    func play(url: URL)
    func pause()
    func seek(to time: TimeInterval)
    func setPlaybackRate(_ rate: Float)
}
```

#### 4.2.2 播放器视图
```swift
struct VideoPlayerView: View {
    @StateObject private var playerManager = VideoPlayerManager()
    let videoInfo: VideoInfo
    
    var body: some View {
        ZStack {
            VideoPlayer(player: playerManager.player)
            PlayerControlsView(playerManager: playerManager)
        }
    }
}
```

### 4.3 数据模型设计

#### 4.3.1 视频信息模型
```swift
struct VideoInfo: Codable, Identifiable {
    let id: String
    let name: String
    let pic: String?
    let actor: String?
    let director: String?
    let year: Int?
    let area: String?
    let lang: String?
    let note: String?
    let des: String?
    let type: String?
    let seriesFlags: [SeriesFlag]
    let seriesMap: [String: [VideoSeries]]
}

struct VideoSeries: Codable, Identifiable {
    let id = UUID()
    let name: String
    let url: String
    var selected: Bool = false
}

struct SeriesFlag: Codable, Identifiable {
    let id = UUID()
    let name: String
    var selected: Bool = false
}
```

#### 4.3.2 搜索结果模型
```swift
struct SearchResult: Codable {
    let page: Int
    let pageCount: Int
    let pageSize: Int
    let recordCount: Int
    let videoList: [VideoInfo]
}
```

### 4.4 网络服务模块

#### 4.4.1 网络管理器
```swift
class NetworkManager {
    static let shared = NetworkManager()
    private let session: URLSession
    
    func request<T: Codable>(
        url: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil
    ) async throws -> T
    
    func downloadData(from url: String) async throws -> Data
}
```

#### 4.4.2 API服务
```swift
protocol VideoAPIService {
    func getHomeData(source: DataSource) async throws -> [VideoInfo]
    func searchVideos(keyword: String, source: DataSource) async throws -> SearchResult
    func getVideoDetail(id: String, source: DataSource) async throws -> VideoInfo
    func getVideoPlayUrls(id: String, source: DataSource) async throws -> [String: [VideoSeries]]
}

class DefaultVideoAPIService: VideoAPIService {
    private let networkManager: NetworkManager
    private let parser: DataParser
    
    // 实现协议方法
}
```

### 4.5 数据解析模块

#### 4.5.1 解析器协议
```swift
protocol DataParser {
    func parseHomeData(_ data: Data, type: DataSourceType) throws -> [VideoInfo]
    func parseSearchResult(_ data: Data, type: DataSourceType) throws -> SearchResult
    func parseVideoDetail(_ data: Data, type: DataSourceType) throws -> VideoInfo
}

class XMLDataParser: DataParser {
    // XML解析实现
}

class JSONDataParser: DataParser {
    // JSON解析实现
}
```

### 4.6 本地存储模块

#### 4.6.1 Core Data模型
```swift
// VideoEntity.swift
@objc(VideoEntity)
class VideoEntity: NSManagedObject {
    @NSManaged var id: String
    @NSManaged var name: String
    @NSManaged var pic: String?
    @NSManaged var watchTime: Date?
    @NSManaged var playProgress: Double
    @NSManaged var isFavorite: Bool
}
```

#### 4.6.2 数据存储管理器
```swift
class DataStorageManager {
    static let shared = DataStorageManager()
    private let container: NSPersistentContainer
    
    func saveWatchHistory(_ video: VideoInfo, progress: Double)
    func getFavorites() -> [VideoInfo]
    func addToFavorites(_ video: VideoInfo)
    func removeFromFavorites(_ video: VideoInfo)
    func getWatchHistory() -> [VideoInfo]
}
```

## 5. 用户界面设计

### 5.1 主界面结构
```swift
struct MainTabView: View {
    var body: some View {
        TabView {
            HomeView()
                .tabItem { Label("首页", systemImage: "house") }
            
            SearchView()
                .tabItem { Label("搜索", systemImage: "magnifyingglass") }
            
            FavoritesView()
                .tabItem { Label("收藏", systemImage: "heart") }
            
            HistoryView()
                .tabItem { Label("历史", systemImage: "clock") }
            
            SettingsView()
                .tabItem { Label("设置", systemImage: "gear") }
        }
    }
}
```

### 5.2 视频列表视图
```swift
struct VideoGridView: View {
    let videos: [VideoInfo]
    let columns = Array(repeating: GridItem(.flexible()), count: 3)
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(videos) { video in
                VideoCardView(video: video)
                    .onTapGesture {
                        // 导航到详情页
                    }
            }
        }
        .padding()
    }
}
```

### 5.3 视频卡片组件
```swift
struct VideoCardView: View {
    let video: VideoInfo
    
    var body: some View {
        VStack(alignment: .leading) {
            AsyncImage(url: URL(string: video.pic ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(3/4, contentMode: .fill)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(3/4, contentMode: .fill)
            }
            .clipped()
            .cornerRadius(8)
            
            Text(video.name)
                .font(.caption)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
    }
}
```

## 6. 技术实现细节

### 6.1 响应式编程
使用Combine框架实现响应式编程：
```swift
class HomeViewModel: ObservableObject {
    @Published var videos: [VideoInfo] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private var cancellables = Set<AnyCancellable>()
    private let videoService: VideoAPIService
    
    func loadHomeData() {
        isLoading = true
        
        videoService.getHomeData(source: currentSource)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] videos in
                    self?.videos = videos
                }
            )
            .store(in: &cancellables)
    }
}
```

### 6.2 依赖注入
```swift
protocol DIContainer {
    var videoService: VideoAPIService { get }
    var storageManager: DataStorageManager { get }
    var networkManager: NetworkManager { get }
}

class DefaultDIContainer: DIContainer {
    lazy var videoService: VideoAPIService = DefaultVideoAPIService(
        networkManager: networkManager,
        parser: JSONDataParser()
    )
    
    lazy var storageManager: DataStorageManager = DataStorageManager.shared
    lazy var networkManager: NetworkManager = NetworkManager.shared
}
```

### 6.3 错误处理
```swift
enum AppError: Error, LocalizedError {
    case networkError(Error)
    case parseError(String)
    case invalidURL
    case noData
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .parseError(let message):
            return "解析错误: \(message)"
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        }
    }
}
```

## 7. 性能优化策略

### 7.1 图片加载优化
```swift
class ImageCache {
    static let shared = ImageCache()
    private let cache = NSCache<NSString, UIImage>()
    
    func image(for url: String) -> UIImage? {
        return cache.object(forKey: url as NSString)
    }
    
    func setImage(_ image: UIImage, for url: String) {
        cache.setObject(image, forKey: url as NSString)
    }
}
```

### 7.2 数据缓存策略
```swift
class DataCache {
    private let cache = NSCache<NSString, NSData>()
    private let fileManager = FileManager.default
    
    func cacheData(_ data: Data, for key: String, expiry: TimeInterval = 3600) {
        // 内存缓存
        cache.setObject(data as NSData, forKey: key as NSString)
        
        // 磁盘缓存
        let url = cacheURL(for: key)
        try? data.write(to: url)
    }
    
    func cachedData(for key: String) -> Data? {
        // 先检查内存缓存
        if let data = cache.object(forKey: key as NSString) {
            return data as Data
        }
        
        // 再检查磁盘缓存
        let url = cacheURL(for: key)
        return try? Data(contentsOf: url)
    }
}
```

### 7.3 视频预加载
```swift
class VideoPreloader {
    private var preloadedPlayers: [String: AVPlayer] = [:]
    
    func preloadVideo(url: String) {
        guard let videoURL = URL(string: url) else { return }
        let player = AVPlayer(url: videoURL)
        preloadedPlayers[url] = player
    }
    
    func getPreloadedPlayer(for url: String) -> AVPlayer? {
        return preloadedPlayers[url]
    }
}
```

## 8. 安全性考虑

### 8.1 网络安全
```swift
class SecureNetworkManager {
    private let session: URLSession
    
    init() {
        let configuration = URLSessionConfiguration.default
        configuration.tlsMinimumSupportedProtocolVersion = .TLSv12
        self.session = URLSession(configuration: configuration)
    }
    
    func secureRequest(url: String) async throws -> Data {
        guard let url = URL(string: url), url.scheme == "https" else {
            throw AppError.invalidURL
        }
        
        let (data, _) = try await session.data(from: url)
        return data
    }
}
```

### 8.2 数据加密
```swift
import CryptoKit

class DataEncryption {
    static func encrypt(data: Data, key: String) throws -> Data {
        let keyData = key.data(using: .utf8)!
        let symmetricKey = SymmetricKey(data: keyData)
        let sealedBox = try AES.GCM.seal(data, using: symmetricKey)
        return sealedBox.combined!
    }
    
    static func decrypt(data: Data, key: String) throws -> Data {
        let keyData = key.data(using: .utf8)!
        let symmetricKey = SymmetricKey(data: keyData)
        let sealedBox = try AES.GCM.SealedBox(combined: data)
        return try AES.GCM.open(sealedBox, using: symmetricKey)
    }
}
```

## 9. 测试策略

### 9.1 单元测试
```swift
import XCTest
@testable import FreeTVPlayer

class VideoAPIServiceTests: XCTestCase {
    var sut: VideoAPIService!
    var mockNetworkManager: MockNetworkManager!
    
    override func setUp() {
        super.setUp()
        mockNetworkManager = MockNetworkManager()
        sut = DefaultVideoAPIService(networkManager: mockNetworkManager)
    }
    
    func testSearchVideos() async throws {
        // Given
        let expectedResult = SearchResult(page: 1, pageCount: 10, pageSize: 20, recordCount: 200, videoList: [])
        mockNetworkManager.mockResult = expectedResult
        
        // When
        let result = try await sut.searchVideos(keyword: "test", source: mockDataSource)
        
        // Then
        XCTAssertEqual(result.page, expectedResult.page)
    }
}
```

### 9.2 UI测试
```swift
import XCTest

class FreeTVPlayerUITests: XCTestCase {
    var app: XCUIApplication!
    
    override func setUp() {
        super.setUp()
        app = XCUIApplication()
        app.launch()
    }
    
    func testSearchFunctionality() {
        // 测试搜索功能
        let searchTab = app.tabBars.buttons["搜索"]
        searchTab.tap()
        
        let searchField = app.searchFields.firstMatch
        searchField.tap()
        searchField.typeText("测试视频")
        
        app.keyboards.buttons["search"].tap()
        
        // 验证搜索结果
        XCTAssertTrue(app.collectionViews.firstMatch.exists)
    }
}
```

## 10. 部署和发布

### 10.1 构建配置
```swift
// BuildConfiguration.swift
enum BuildConfiguration {
    case debug
    case release
    
    var apiBaseURL: String {
        switch self {
        case .debug:
            return "https://debug-api.example.com"
        case .release:
            return "https://api.example.com"
        }
    }
    
    var isLoggingEnabled: Bool {
        switch self {
        case .debug:
            return true
        case .release:
            return false
        }
    }
}
```

### 10.2 App Store发布准备
- 应用图标和截图准备
- 隐私政策和用户协议
- App Store审核指南合规性检查
- 本地化支持

## 11. 开发计划

### 11.1 第一阶段 (MVP)
- [ ] 基础项目架构搭建
- [ ] 数据源配置管理
- [ ] 基础视频播放功能
- [ ] 简单的搜索功能
- [ ] 基础UI界面

### 11.2 第二阶段 (功能完善)
- [ ] 收藏和历史记录
- [ ] 高级搜索和筛选
- [ ] 播放器控制优化
- [ ] 缓存和离线功能
- [ ] 设置和个性化

### 11.3 第三阶段 (优化和扩展)
- [ ] 性能优化
- [ ] macOS适配
- [ ] Apple TV支持
- [ ] 高级功能(字幕、弹幕等)
- [ ] 社交功能

## 12. 总结

FreeTVPlayer的设计充分考虑了iOS/macOS平台的特性，采用现代化的Swift技术栈和架构模式。通过模块化设计和清晰的分层架构，确保代码的可维护性和可扩展性。同时，注重用户体验和性能优化，为用户提供流畅、稳定的视频播放体验。