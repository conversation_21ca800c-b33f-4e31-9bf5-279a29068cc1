// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		63EF7DAE2E35350000727E48 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 63EF7D972E3534FF00727E48 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 63EF7D9E2E3534FF00727E48;
			remoteInfo = free_tv_player;
		};
		63EF7DB82E35350000727E48 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 63EF7D972E3534FF00727E48 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 63EF7D9E2E3534FF00727E48;
			remoteInfo = free_tv_player;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		63EF7D9F2E3534FF00727E48 /* free_tv_player.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = free_tv_player.app; sourceTree = BUILT_PRODUCTS_DIR; };
		63EF7DAD2E35350000727E48 /* free_tv_playerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = free_tv_playerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		63EF7DB72E35350000727E48 /* free_tv_playerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = free_tv_playerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		63EF7DA12E3534FF00727E48 /* free_tv_player */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = free_tv_player;
			sourceTree = "<group>";
		};
		63EF7DB02E35350000727E48 /* free_tv_playerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = free_tv_playerTests;
			sourceTree = "<group>";
		};
		63EF7DBA2E35350000727E48 /* free_tv_playerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = free_tv_playerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		63EF7D9C2E3534FF00727E48 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DAA2E35350000727E48 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DB42E35350000727E48 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		63EF7D962E3534FF00727E48 = {
			isa = PBXGroup;
			children = (
				63EF7DA12E3534FF00727E48 /* free_tv_player */,
				63EF7DB02E35350000727E48 /* free_tv_playerTests */,
				63EF7DBA2E35350000727E48 /* free_tv_playerUITests */,
				63EF7DA02E3534FF00727E48 /* Products */,
			);
			sourceTree = "<group>";
		};
		63EF7DA02E3534FF00727E48 /* Products */ = {
			isa = PBXGroup;
			children = (
				63EF7D9F2E3534FF00727E48 /* free_tv_player.app */,
				63EF7DAD2E35350000727E48 /* free_tv_playerTests.xctest */,
				63EF7DB72E35350000727E48 /* free_tv_playerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		63EF7D9E2E3534FF00727E48 /* free_tv_player */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 63EF7DC12E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_player" */;
			buildPhases = (
				63EF7D9B2E3534FF00727E48 /* Sources */,
				63EF7D9C2E3534FF00727E48 /* Frameworks */,
				63EF7D9D2E3534FF00727E48 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				63EF7DA12E3534FF00727E48 /* free_tv_player */,
			);
			name = free_tv_player;
			packageProductDependencies = (
			);
			productName = free_tv_player;
			productReference = 63EF7D9F2E3534FF00727E48 /* free_tv_player.app */;
			productType = "com.apple.product-type.application";
		};
		63EF7DAC2E35350000727E48 /* free_tv_playerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 63EF7DC42E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_playerTests" */;
			buildPhases = (
				63EF7DA92E35350000727E48 /* Sources */,
				63EF7DAA2E35350000727E48 /* Frameworks */,
				63EF7DAB2E35350000727E48 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				63EF7DAF2E35350000727E48 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				63EF7DB02E35350000727E48 /* free_tv_playerTests */,
			);
			name = free_tv_playerTests;
			packageProductDependencies = (
			);
			productName = free_tv_playerTests;
			productReference = 63EF7DAD2E35350000727E48 /* free_tv_playerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		63EF7DB62E35350000727E48 /* free_tv_playerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 63EF7DC72E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_playerUITests" */;
			buildPhases = (
				63EF7DB32E35350000727E48 /* Sources */,
				63EF7DB42E35350000727E48 /* Frameworks */,
				63EF7DB52E35350000727E48 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				63EF7DB92E35350000727E48 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				63EF7DBA2E35350000727E48 /* free_tv_playerUITests */,
			);
			name = free_tv_playerUITests;
			packageProductDependencies = (
			);
			productName = free_tv_playerUITests;
			productReference = 63EF7DB72E35350000727E48 /* free_tv_playerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		63EF7D972E3534FF00727E48 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					63EF7D9E2E3534FF00727E48 = {
						CreatedOnToolsVersion = 16.4;
					};
					63EF7DAC2E35350000727E48 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 63EF7D9E2E3534FF00727E48;
					};
					63EF7DB62E35350000727E48 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 63EF7D9E2E3534FF00727E48;
					};
				};
			};
			buildConfigurationList = 63EF7D9A2E3534FF00727E48 /* Build configuration list for PBXProject "free_tv_player" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 63EF7D962E3534FF00727E48;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 63EF7DA02E3534FF00727E48 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				63EF7D9E2E3534FF00727E48 /* free_tv_player */,
				63EF7DAC2E35350000727E48 /* free_tv_playerTests */,
				63EF7DB62E35350000727E48 /* free_tv_playerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		63EF7D9D2E3534FF00727E48 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DAB2E35350000727E48 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DB52E35350000727E48 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		63EF7D9B2E3534FF00727E48 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DA92E35350000727E48 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		63EF7DB32E35350000727E48 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		63EF7DAF2E35350000727E48 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 63EF7D9E2E3534FF00727E48 /* free_tv_player */;
			targetProxy = 63EF7DAE2E35350000727E48 /* PBXContainerItemProxy */;
		};
		63EF7DB92E35350000727E48 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 63EF7D9E2E3534FF00727E48 /* free_tv_player */;
			targetProxy = 63EF7DB82E35350000727E48 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		63EF7DBF2E35350000727E48 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		63EF7DC02E35350000727E48 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		63EF7DC22E35350000727E48 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = free_tv_player/free_tv_player.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-player";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		63EF7DC32E35350000727E48 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = free_tv_player/free_tv_player.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-player";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		63EF7DC52E35350000727E48 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-playerTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/free_tv_player.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/free_tv_player";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		63EF7DC62E35350000727E48 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-playerTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/free_tv_player.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/free_tv_player";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		63EF7DC82E35350000727E48 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-playerUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = free_tv_player;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		63EF7DC92E35350000727E48 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 9XNPP9W6W2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.huang.free-tv-playerUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = free_tv_player;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		63EF7D9A2E3534FF00727E48 /* Build configuration list for PBXProject "free_tv_player" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				63EF7DBF2E35350000727E48 /* Debug */,
				63EF7DC02E35350000727E48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		63EF7DC12E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_player" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				63EF7DC22E35350000727E48 /* Debug */,
				63EF7DC32E35350000727E48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		63EF7DC42E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_playerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				63EF7DC52E35350000727E48 /* Debug */,
				63EF7DC62E35350000727E48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		63EF7DC72E35350000727E48 /* Build configuration list for PBXNativeTarget "free_tv_playerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				63EF7DC82E35350000727E48 /* Debug */,
				63EF7DC92E35350000727E48 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 63EF7D972E3534FF00727E48 /* Project object */;
}
