# TVBox 技术文档

## 1. 项目概述

TVBox是一款基于Android平台的开源影视播放器应用，支持多种视频源和播放协议。项目采用模块化架构设计，支持JavaScript、Python等多种脚本语言扩展。

## 2. 技术架构

### 2.1 整体架构
- **应用层**: Android原生应用，使用Java/Kotlin开发
- **核心模块**: 包含播放器、数据源管理、UI界面等
- **扩展模块**: JavaScript引擎(QuickJS)、Python引擎(Chaquo Python)
- **第三方库**: 网络请求、数据库、UI组件等

### 2.2 模块结构
```
TVBox/
├── app/                    # 主应用模块
│   ├── src/main/java/com/github/tvbox/osc/
│   │   ├── api/           # API配置和数据源管理
│   │   ├── base/          # 基础类和应用入口
│   │   ├── bean/          # 数据模型
│   │   ├── player/        # 播放器模块
│   │   ├── ui/            # 用户界面
│   │   ├── util/          # 工具类
│   │   └── ...
├── quickjs/               # JavaScript引擎模块
├── pyramid/               # Python引擎模块
└── xwalk/                 # WebView引擎
```

## 3. 核心功能模块

### 3.1 数据源管理 (ApiConfig)
- **配置加载**: 支持本地和远程配置文件
- **数据源类型**: 
  - XML格式数据源 (type=0)
  - JSON格式数据源 (type=1) 
  - Spider爬虫数据源 (type=3)
- **加密支持**: AES加密、Base64编码
- **缓存机制**: 本地配置缓存，提升加载速度

### 3.2 播放器架构
支持多种播放器内核：
- **系统播放器** (type=0): Android原生MediaPlayer
- **IJK播放器** (type=1): 基于FFmpeg的开源播放器
- **ExoPlayer** (type=2): Google官方媒体播放器
- **第三方播放器**: MX Player、Reex Player、Kodi等

播放器特性：
- 硬件/软件解码切换
- 多种视频格式支持
- 字幕支持
- 弹幕功能
- 倍速播放

### 3.3 数据模型设计

#### SourceBean (数据源)
```java
public class SourceBean {
    private String key;          // 数据源标识
    private String name;         // 显示名称
    private String api;          // API地址
    private int type;            // 数据源类型
    private int searchable;      // 是否支持搜索
    private int quickSearch;     // 是否支持快速搜索
    private String ext;          // 扩展配置
    private String jar;          // 自定义JAR包
    // ...
}
```

#### VodInfo (视频信息)
```java
public class VodInfo {
    public String id;                    // 视频ID
    public String name;                  // 视频名称
    public String pic;                   // 封面图片
    public String actor;                 // 演员
    public String director;              // 导演
    public LinkedHashMap<String, List<VodSeries>> seriesMap; // 播放列表
    // ...
}
```

### 3.4 网络请求架构
- **HTTP客户端**: OkHttp3
- **DNS优化**: 支持DoH (DNS over HTTPS)
- **代理支持**: HTTP/SOCKS代理
- **SSL兼容**: 自定义SSL配置
- **请求拦截**: 自定义请求头、User-Agent等

### 3.5 脚本引擎支持

#### JavaScript引擎 (QuickJS)
- 轻量级JavaScript运行时
- 支持ES2020语法
- 用于数据源解析和内容抓取

#### Python引擎 (Chaquo Python)
- 完整的Python 3.8运行环境
- 支持第三方库：requests、lxml、beautifulsoup4等
- 用于复杂的网页解析和数据处理

## 4. 技术特性

### 4.1 多媒体支持
- **视频格式**: MP4、MKV、AVI、FLV、M3U8等
- **音频格式**: MP3、AAC、FLAC等
- **流媒体协议**: HTTP、HTTPS、RTMP、RTSP等
- **字幕格式**: SRT、ASS、VTT等

### 4.2 用户界面
- **Material Design**: 遵循Android设计规范
- **TV适配**: 支持Android TV和机顶盒
- **主题系统**: 多种主题风格
- **多语言**: 中文、英文支持

### 4.3 数据存储
- **Room数据库**: 本地数据持久化
- **SharedPreferences**: 配置信息存储
- **文件缓存**: 视频缓存和配置缓存

### 4.4 安全特性
- **配置加密**: AES加密保护配置文件
- **网络安全**: SSL/TLS加密传输
- **权限控制**: 最小权限原则

## 5. 依赖库分析

### 5.1 核心依赖
```gradle
// 网络请求
implementation 'com.squareup.okhttp3:okhttp:3.12.11'
implementation 'com.lzy.net:okgo:3.0.4'

// 媒体播放
implementation "androidx.media3:media3-exoplayer:1.3.1"
implementation "androidx.media3:media3-ui:1.3.1"

// 数据库
implementation 'androidx.room:room-runtime:2.6.1'
kapt 'androidx.room:room-compiler:2.6.1'

// UI组件
implementation 'com.google.android.material:material:1.12.0'
implementation 'androidx.recyclerview:recyclerview:1.3.2'

// 图片加载
implementation 'com.github.bumptech.glide:glide:4.16.0'

// JSON解析
implementation 'com.google.code.gson:gson:2.10.1'
```

### 5.2 扩展依赖
```gradle
// JavaScript引擎
implementation project(path: ':quickjs')

// Python引擎
pythonImplementation project(":pyramid")

// Web服务器
implementation 'com.yanzhenjie.andserver:api:2.1.12'

// 弹幕
implementation 'com.github.ctiao:DanmakuFlameMaster:0.9.25'
```

## 6. 构建配置

### 6.1 多渠道构建
```gradle
flavorDimensions += ["abi", "brand", "mode"]
productFlavors {
    armeabi { dimension = "abi" }
    arm64 { dimension = "abi" }
    generic { dimension = "brand" }
    hisense { dimension = "brand" }
    normal { dimension = "mode" }
    python { dimension = "mode" }
}
```

### 6.2 代码混淆
- 启用ProGuard代码混淆
- 保护核心算法和配置
- 减小APK体积

## 7. 性能优化

### 7.1 内存优化
- 图片缓存管理
- 视频缓存清理
- 内存泄漏防护

### 7.2 网络优化
- 连接池复用
- 请求缓存
- 断点续传

### 7.3 启动优化
- 延迟初始化
- 异步加载
- 预加载机制

## 8. 扩展性设计

### 8.1 插件系统
- JAR包动态加载
- JavaScript脚本扩展
- Python脚本扩展

### 8.2 数据源扩展
- 标准化API接口
- 多种数据格式支持
- 自定义解析规则

## 9. 安全考虑

### 9.1 数据安全
- 配置文件加密
- 敏感信息保护
- 安全传输协议

### 9.2 应用安全
- 代码混淆
- 反调试保护
- 权限最小化

## 10. 总结

TVBox采用了现代化的Android开发技术栈，具有良好的架构设计和扩展性。其模块化的设计使得功能扩展和维护变得相对容易，多脚本引擎的支持为数据源的多样化提供了强大的技术基础。