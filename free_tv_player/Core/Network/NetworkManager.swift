//
//  NetworkManager.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Network Manager Protocol
protocol NetworkManagerProtocol {
    func request<T: Codable>(
        _ endpoint: APIEndpoint,
        responseType: T.Type
    ) -> AnyPublisher<T, AppError>
    
    func downloadData(from url: URL) -> AnyPublisher<Data, AppError>
    func downloadImage(from url: URL) -> AnyPublisher<Data, AppError>
}

// MARK: - Network Manager Implementation
class NetworkManager: NetworkManagerProtocol {
    static let shared = NetworkManager()
    
    private let session: URLSession
    private let decoder: JSONDecoder
    private let cache: URLCache
    
    private init() {
        // Configure URL Session
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = AppConstants.requestTimeout
        config.timeoutIntervalForResource = AppConstants.requestTimeout
        config.waitsForConnectivity = true
        config.allowsCellularAccess = true
        
        // Configure cache
        self.cache = URLCache(
            memoryCapacity: AppConstants.imageCacheLimit,
            diskCapacity: AppConstants.imageCacheLimit
        )
        config.urlCache = cache
        config.requestCachePolicy = .returnCacheDataElseLoad
        
        self.session = URLSession(configuration: config)
        
        // Configure JSON Decoder
        self.decoder = JSONDecoder()
        self.decoder.dateDecodingStrategy = .iso8601
        self.decoder.keyDecodingStrategy = .convertFromSnakeCase
    }
    
    func request<T: Codable>(
        _ endpoint: APIEndpoint,
        responseType: T.Type
    ) -> AnyPublisher<T, AppError> {
        guard let request = buildRequest(from: endpoint) else {
            return Fail(error: AppError.url(.invalidURL))
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: request)
            .tryMap { [weak self] data, response in
                try self?.handleResponse(data: data, response: response) ?? data
            }
            .decode(type: T.self, decoder: decoder)
            .mapError { error in
                self.mapError(error)
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func downloadData(from url: URL) -> AnyPublisher<Data, AppError> {
        session.dataTaskPublisher(for: url)
            .tryMap { [weak self] data, response in
                try self?.handleResponse(data: data, response: response) ?? data
            }
            .mapError { error in
                self.mapError(error)
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func downloadImage(from url: URL) -> AnyPublisher<Data, AppError> {
        // Check cache first
        let request = URLRequest(url: url)
        if let cachedResponse = cache.cachedResponse(for: request) {
            return Just(cachedResponse.data)
                .setFailureType(to: AppError.self)
                .eraseToAnyPublisher()
        }
        
        return downloadData(from: url)
    }
    
    // MARK: - Private Methods
    private func buildRequest(from endpoint: APIEndpoint) -> URLRequest? {
        guard let url = endpoint.url else { return nil }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.timeoutInterval = AppConstants.requestTimeout
        
        // Add headers
        endpoint.headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // Add default headers
        request.setValue(AppConstants.defaultUserAgent, forHTTPHeaderField: "User-Agent")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        // Add body for POST requests
        if let body = endpoint.body {
            request.httpBody = body
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        }
        
        return request
    }
    
    private func handleResponse(data: Data, response: URLResponse) throws -> Data {
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AppError.data(.invalidResponse)
        }
        
        switch httpResponse.statusCode {
        case 200...299:
            return data
        case 400:
            throw AppError.network(.serverError)
        case 401:
            throw AppError.authenticationError
        case 403:
            throw AppError.permissionDenied
        case 404:
            throw AppError.data(.notFound)
        case 500...599:
            throw AppError.network(.serverError)
        default:
            throw AppError.network(.unknown)
        }
    }
    
    private func mapError(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet:
                return AppError.network(.noConnection)
            case .timedOut:
                return AppError.timeout
            case .cannotFindHost, .cannotConnectToHost:
                return AppError.network(.serverError)
            default:
                return AppError.network(.unknown)
            }
        }
        
        if error is DecodingError {
            return AppError.parsing(.invalidFormat)
        }
        
        return AppError.network(.unknown)
    }
}

// MARK: - API Endpoint
struct APIEndpoint {
    let path: String
    let method: HTTPMethod
    let headers: [String: String]?
    let queryItems: [URLQueryItem]?
    let body: Data?
    
    var url: URL? {
        var components = URLComponents()
        components.scheme = "https"
        components.host = "api.example.com" // 这里需要根据实际API替换
        components.path = path
        components.queryItems = queryItems
        return components.url
    }
    
    init(
        path: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        queryItems: [URLQueryItem]? = nil,
        body: Data? = nil
    ) {
        self.path = path
        self.method = method
        self.headers = headers
        self.queryItems = queryItems
        self.body = body
    }
}

// MARK: - HTTP Method
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - Network Reachability
class NetworkReachability: ObservableObject {
    @Published var isConnected = true
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkReachability")
    
    init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
            }
        }
        monitor.start(queue: queue)
    }
    
    deinit {
        monitor.cancel()
    }
}

// MARK: - Network Extensions
import Network

extension NWPath.Status {
    var isConnected: Bool {
        return self == .satisfied
    }
}