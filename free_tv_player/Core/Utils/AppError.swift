//
//  AppError.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import Foundation

enum AppError: Error, LocalizedError {
    case networkError(Error)
    case parseError(String)
    case invalidURL(String)
    case noData
    case invalidDataSource
    case playerError(String)
    case storageError(String)
    case configurationError(String)
    case authenticationError
    case permissionDenied
    case timeout
    case unknown(Error)
    
    // New error types for DataSourceService
    case url(URLError)
    case data(DataError)
    case parsing(ParsingError)
    case configuration(ConfigurationError)
    case network(NetworkError)
    case storage(StorageError)
    
    enum URLError {
        case invalidURL
    }
    
    enum DataError {
        case processingFailed
        case encodingFailed
        case invalidResponse
        case notFound
        case notSupported
    }
    
    enum ParsingError {
        case invalidFormat
        case unsupportedFormat
    }
    
    enum ConfigurationError {
        case invalidFormat
        case notFound
    }
    
    enum NetworkError {
        case noConnection
        case serverError
        case unknown
    }
    
    enum StorageError {
        case deleteFailed
        case clearFailed
        case saveFailed
        case loadFailed
    }
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .parseError(let message):
            return "解析错误: \(message)"
        case .invalidURL(let url):
            return "无效的URL: \(url)"
        case .noData:
            return "没有数据"
        case .invalidDataSource:
            return "无效的数据源"
        case .playerError(let message):
            return "播放器错误: \(message)"
        case .storageError(let message):
            return "存储错误: \(message)"
        case .configurationError(let message):
            return "配置错误: \(message)"
        case .authenticationError:
            return "认证失败"
        case .permissionDenied:
            return "权限被拒绝"
        case .timeout:
            return "请求超时"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        case .url(let urlError):
            switch urlError {
            case .invalidURL:
                return "无效的URL"
            }
        case .data(let dataError):
            switch dataError {
            case .processingFailed:
                return "数据处理失败"
            case .encodingFailed:
                return "数据编码失败"
            case .invalidResponse:
                return "无效的响应数据"
            case .notFound:
                return "数据未找到"
            case .notSupported:
                return "不支持的数据类型"
            }
        case .parsing(let parsingError):
            switch parsingError {
            case .invalidFormat:
                return "数据格式无效"
            case .unsupportedFormat:
                return "不支持的数据格式"
            }
        case .configuration(let configError):
            switch configError {
            case .invalidFormat:
                return "配置格式无效"
            case .notFound:
                return "配置未找到"
            }
        case .network(let networkError):
            switch networkError {
            case .noConnection:
                return "网络连接失败"
            case .serverError:
                return "服务器错误"
            case .unknown:
                return "未知网络错误"
            }
        case .storage(let storageError):
            switch storageError {
            case .deleteFailed:
                return "删除失败"
            case .clearFailed:
                return "清除失败"
            case .saveFailed:
                return "保存失败"
            case .loadFailed:
                return "加载失败"
            }
        }
    }
    
    var failureReason: String? {
        switch self {
        case .networkError:
            return "网络连接失败或服务器无响应"
        case .parseError:
            return "数据格式不正确或解析失败"
        case .invalidURL:
            return "提供的URL格式不正确"
        case .noData:
            return "服务器返回空数据"
        case .invalidDataSource:
            return "数据源配置不正确"
        case .playerError:
            return "视频播放器遇到问题"
        case .storageError:
            return "本地存储操作失败"
        case .configurationError:
            return "应用配置有误"
        case .authenticationError:
            return "身份验证失败"
        case .permissionDenied:
            return "缺少必要的系统权限"
        case .timeout:
            return "操作超时"
        case .unknown:
            return "发生了未预期的错误"
        case .url:
            return "URL处理失败"
        case .data:
            return "数据处理失败"
        case .parsing:
            return "数据解析失败"
        case .configuration:
            return "配置处理失败"
        case .network:
            return "网络操作失败"
        case .storage:
            return "存储操作失败"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError:
            return "请检查网络连接后重试"
        case .parseError:
            return "请检查数据源格式是否正确"
        case .invalidURL:
            return "请检查URL格式是否正确"
        case .noData:
            return "请稍后重试或联系数据源提供方"
        case .invalidDataSource:
            return "请检查数据源配置"
        case .playerError:
            return "请重启播放器或更换播放源"
        case .storageError:
            return "请检查设备存储空间"
        case .configurationError:
            return "请重新配置应用设置"
        case .authenticationError:
            return "请重新登录"
        case .permissionDenied:
            return "请在设置中授予必要权限"
        case .timeout:
            return "请检查网络连接后重试"
        case .unknown:
            return "请重启应用或联系技术支持"
        case .url:
            return "请检查URL格式是否正确"
        case .data:
            return "请重试或检查数据完整性"
        case .parsing:
            return "请检查数据格式是否正确"
        case .configuration:
            return "请检查配置文件格式"
        case .network:
            return "请检查网络连接后重试"
        case .storage:
            return "请检查设备存储空间或重启应用"
        }
    }
}