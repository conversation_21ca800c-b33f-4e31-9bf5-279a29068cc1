//
//  AppConstants.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation

struct AppConstants {
    
    // MARK: - App Info
    static let appName = "FreeTVPlayer"
    static let appVersion = "1.0.0"
    
    struct App {
        static let name = "FreeTVPlayer"
        static let version = "1.0.0"
    }
    
    // MARK: - Network
    static let requestTimeout: TimeInterval = 30.0
    static let defaultUserAgent = "FreeTVPlayer/1.0.0 (iOS)"
    
    // MARK: - Cache
    static let imageCacheLimit = 100 * 1024 * 1024 // 100MB
    static let dataCacheExpiry: TimeInterval = 3600 // 1 hour
    
    // MARK: - Player
    static let defaultPlaybackRate: Float = 1.0
    static let seekInterval: TimeInterval = 10.0
    
    // MARK: - UI
    static let gridColumns = 3
    static let cardAspectRatio: Double = 3.0/4.0
    static let animationDuration: Double = 0.3
    
    // MARK: - Storage Keys
    struct StorageKeys {
        static let dataSources = "app.datasources"
        static let currentSource = "app.current_source"
        static let playbackSettings = "app.playback_settings"
        static let userPreferences = "app.user_preferences"
    }
    
    struct Storage {
        static let configurationKey = "app.configuration"
        static let showThumbnailsKey = "app.show_thumbnails"
        static let lastConfigURLKey = "app.last_config_url"
        static let autoUpdateConfigKey = "app.auto_update_config"
        static let autoPlayKey = "app.auto_play"
        static let backgroundPlayKey = "app.background_play"
        static let gridColumnsKey = "app.grid_columns"
        static let historyKey = "app.history"
        static let favoritesKey = "app.favorites"
        static let playerTypeKey = "app.player_type"
    }
    
    // MARK: - API
    struct API {
        static let defaultTimeout: TimeInterval = 30.0
        static let maxRetryCount = 3
    }
    
    // MARK: - Validation
    struct Validation {
        static let minSearchLength = 2
        static let maxSearchLength = 50
    }
}