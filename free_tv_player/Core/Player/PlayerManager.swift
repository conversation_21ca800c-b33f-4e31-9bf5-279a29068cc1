//
//  PlayerManager.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation
import AVFoundation
import AVKit
import Combine
import AppKit
import MediaPlayer

// MARK: - Player Manager Protocol
protocol PlayerManagerProtocol: ObservableObject {
    var isPlaying: Bool { get }
    var currentTime: TimeInterval { get }
    var duration: TimeInterval { get }
    var progress: Double { get }
    var volume: Float { get set }
    var playbackRate: Float { get set }
    var isBuffering: Bool { get }
    var error: Error? { get }
    
    func play(url: URL)
    func play()
    func pause()
    func stop()
    func seek(to time: TimeInterval)
    func seekForward(_ seconds: TimeInterval)
    func seekBackward(_ seconds: TimeInterval)
    func togglePlayPause()
}

// MARK: - Player Manager
class PlayerManager: NSObject, PlayerManagerProtocol {
    static let shared = PlayerManager()
    
    // MARK: - Published Properties
    @Published var isPlaying = false
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0
    @Published var progress: Double = 0
    @Published var volume: Float = 1.0 {
        didSet {
            player?.volume = volume
        }
    }
    @Published var playbackRate: Float = 1.0 {
        didSet {
            player?.rate = isPlaying ? playbackRate : 0
        }
    }
    @Published var isBuffering = false
    @Published var error: Error?
    
    // MARK: - Private Properties
    private var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Player Configuration
    private let userPreferences = UserPreferences.shared
    
    override init() {
        super.init()
        setupAudioSession()
        setupNotifications()
    }
    
    deinit {
        cleanup()
    }
    
    // MARK: - Public Methods
    func play(url: URL) {
        cleanup()
        
        let asset = AVURLAsset(url: url)
        playerItem = AVPlayerItem(asset: asset)
        player = AVPlayer(playerItem: playerItem)
        
        setupPlayerObservers()
        setupPlayerItem()
        
        player?.volume = volume
        player?.play()
        
        isPlaying = true
        error = nil
    }
    
    func play() {
        guard let player = player else { return }
        player.rate = playbackRate
        isPlaying = true
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
    }
    
    func stop() {
        player?.pause()
        player?.seek(to: .zero)
        isPlaying = false
        currentTime = 0
        progress = 0
    }
    
    func seek(to time: TimeInterval) {
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player?.seek(to: cmTime) { [weak self] _ in
            self?.updateProgress()
        }
    }
    
    func seekForward(_ seconds: TimeInterval) {
        let newTime = currentTime + seconds
        seek(to: min(newTime, duration))
    }
    
    func seekBackward(_ seconds: TimeInterval) {
        let newTime = currentTime - seconds
        seek(to: max(newTime, 0))
    }
    
    func togglePlayPause() {
        if isPlaying {
            pause()
        } else {
            play()
        }
    }
    
    // MARK: - Private Methods
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .moviePlayback)
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleInterruption),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )
    }
    
    private func setupPlayerObservers() {
        guard let player = player else { return }
        
        // Time observer
        let interval = CMTime(seconds: 0.5, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.updateCurrentTime(time)
        }
        
        // Status observer
        player.publisher(for: \.timeControlStatus)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                self?.handleTimeControlStatusChange(status)
            }
            .store(in: &cancellables)
    }
    
    private func setupPlayerItem() {
        guard let playerItem = playerItem else { return }
        
        // Status observer
        playerItem.publisher(for: \.status)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                self?.handlePlayerItemStatusChange(status)
            }
            .store(in: &cancellables)
        
        // Duration observer
        playerItem.publisher(for: \.duration)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] duration in
                self?.updateDuration(duration)
            }
            .store(in: &cancellables)
        
        // Buffering observer
        playerItem.publisher(for: \.isPlaybackBufferEmpty)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isEmpty in
                self?.isBuffering = isEmpty
            }
            .store(in: &cancellables)
        
        // Error observer
        playerItem.publisher(for: \.error)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] error in
                if let error = error {
                    self?.handlePlayerError(error)
                }
            }
            .store(in: &cancellables)
    }
    
    private func updateCurrentTime(_ time: CMTime) {
        guard time.isValid && !time.isIndefinite else { return }
        currentTime = time.seconds
        updateProgress()
    }
    
    private func updateDuration(_ duration: CMTime) {
        guard duration.isValid && !duration.isIndefinite else { return }
        self.duration = duration.seconds
        updateProgress()
    }
    
    private func updateProgress() {
        guard duration > 0 else {
            progress = 0
            return
        }
        progress = currentTime / duration
    }
    
    private func handleTimeControlStatusChange(_ status: AVPlayer.TimeControlStatus) {
        switch status {
        case .playing:
            isPlaying = true
            isBuffering = false
        case .paused:
            isPlaying = false
            isBuffering = false
        case .waitingToPlayAtSpecifiedRate:
            isBuffering = true
        @unknown default:
            break
        }
    }
    
    private func handlePlayerItemStatusChange(_ status: AVPlayerItem.Status) {
        switch status {
        case .readyToPlay:
            error = nil
        case .failed:
            if let error = playerItem?.error {
                handlePlayerError(error)
            }
        case .unknown:
            break
        @unknown default:
            break
        }
    }
    
    private func handlePlayerError(_ error: Error) {
        self.error = error
        isPlaying = false
        isBuffering = false
        print("Player error: \(error)")
    }
    
    private func cleanup() {
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        cancellables.removeAll()
        player?.pause()
        player = nil
        playerItem = nil
    }
    
    // MARK: - Notification Handlers
    @objc private func handleInterruption(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            pause()
        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) && userPreferences.autoPlay {
                    play()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func handleRouteChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Headphones were unplugged
            pause()
        default:
            break
        }
    }
    
    @objc private func playerDidFinishPlaying(_ notification: Notification) {
        isPlaying = false
        currentTime = duration
        progress = 1.0
        
        // Auto replay if enabled
        if userPreferences.autoPlay {
            seek(to: 0)
            play()
        }
    }
}

// MARK: - Player Controls
extension PlayerManager {
    func setPlaybackSpeed(_ speed: Float) {
        playbackRate = speed
    }
    
    func skipToNext() {
        // Implementation for playlist support
    }
    
    func skipToPrevious() {
        // Implementation for playlist support
    }
    
    func toggleMute() {
        volume = volume > 0 ? 0 : 1.0
    }
    
    func adjustVolume(_ delta: Float) {
        volume = max(0, min(1, volume + delta))
    }
}

// MARK: - Picture in Picture Support
extension PlayerManager {
    var supportsPictureInPicture: Bool {
        return AVPictureInPictureController.isPictureInPictureSupported()
    }
    
    func enablePictureInPicture() -> AVPictureInPictureController? {
        guard let player = player,
              supportsPictureInPicture else {
            return nil
        }
        
        let playerLayer = AVPlayerLayer(player: player)
        return AVPictureInPictureController(playerLayer: playerLayer)
    }
}

// MARK: - Background Playback
extension PlayerManager {
    func enableBackgroundPlayback() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .moviePlayback, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Failed to enable background playback: \(error)")
        }
    }
    
    func setupRemoteCommandCenter() {
        let commandCenter = MPRemoteCommandCenter.shared()
        
        commandCenter.playCommand.addTarget { [weak self] _ in
            self?.play()
            return .success
        }
        
        commandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pause()
            return .success
        }
        
        commandCenter.skipForwardCommand.preferredIntervals = [15]
        commandCenter.skipForwardCommand.addTarget { [weak self] event in
            if let skipEvent = event as? MPSkipIntervalCommandEvent {
                self?.seekForward(skipEvent.interval)
            }
            return .success
        }
        
        commandCenter.skipBackwardCommand.preferredIntervals = [15]
        commandCenter.skipBackwardCommand.addTarget { [weak self] event in
            if let skipEvent = event as? MPSkipIntervalCommandEvent {
                self?.seekBackward(skipEvent.interval)
            }
            return .success
        }
        
        commandCenter.changePlaybackPositionCommand.addTarget { [weak self] event in
            if let positionEvent = event as? MPChangePlaybackPositionCommandEvent {
                self?.seek(to: positionEvent.positionTime)
            }
            return .success
        }
    }
    
    func updateNowPlayingInfo(title: String, artist: String? = nil, artwork: UIImage? = nil) {
        var nowPlayingInfo = [String: Any]()
        nowPlayingInfo[MPMediaItemPropertyTitle] = title
        
        if let artist = artist {
            nowPlayingInfo[MPMediaItemPropertyArtist] = artist
        }
        
        if let artwork = artwork {
            nowPlayingInfo[MPMediaItemPropertyArtwork] = MPMediaItemArtwork(boundsSize: artwork.size) { _ in
                return artwork
            }
        }
        
        nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = duration
        nowPlayingInfo[MPNowPlayingInfoPropertyElapsedPlaybackTime] = currentTime
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = isPlaying ? playbackRate : 0
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

// MARK: - Player State
enum PlayerState {
    case idle
    case loading
    case ready
    case playing
    case paused
    case buffering
    case error(Error)
    case finished
}

// MARK: - Playback Speed
enum PlaybackSpeed: Float, CaseIterable {
    case quarter = 0.25
    case half = 0.5
    case normal = 1.0
    case oneAndQuarter = 1.25
    case oneAndHalf = 1.5
    case double = 2.0
    
    var displayName: String {
        switch self {
        case .quarter: return "0.25x"
        case .half: return "0.5x"
        case .normal: return "1.0x"
        case .oneAndQuarter: return "1.25x"
        case .oneAndHalf: return "1.5x"
        case .double: return "2.0x"
        }
    }
}

import MediaPlayer