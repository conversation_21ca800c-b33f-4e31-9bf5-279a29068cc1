//
//  VideoContent.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation

// MARK: - Video Content
struct VideoContent: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let type: String?
    let pic: String?
    let lang: String?
    let area: String?
    let year: String?
    let note: String?
    let actor: String?
    let director: String?
    let des: String?
    let last: String?
    let dt: String?
    let tid: Int?
    let typeName: String?
    let state: Int?
    let remarks: String?
    let serial: Int?
    let doubanScore: String?
    let vodPlayFrom: String?
    let vodPlayUrl: String?
    let vodDownFrom: String?
    let vodDownUrl: String?
    
    // Computed properties
    var imageURL: URL? {
        guard let pic = pic, !pic.isEmpty else { return nil }
        return URL(string: pic)
    }
    
    var hasImage: Bool {
        return imageURL != nil
    }
    
    var displayYear: String {
        return year ?? "未知"
    }
    
    var displayArea: String {
        return area ?? "未知"
    }
    
    var displayType: String {
        return typeName ?? type ?? "未知"
    }
    
    var displayDescription: String {
        return des ?? "暂无简介"
    }
    
    var playUrls: [PlayUrl] {
        guard let vodPlayUrl = vodPlayUrl, !vodPlayUrl.isEmpty else { return [] }
        return parsePlayUrls(vodPlayUrl)
    }
    
    var downloadUrls: [PlayUrl] {
        guard let vodDownUrl = vodDownUrl, !vodDownUrl.isEmpty else { return [] }
        return parsePlayUrls(vodDownUrl)
    }
    
    var playFromSources: [String] {
        guard let vodPlayFrom = vodPlayFrom, !vodPlayFrom.isEmpty else { return [] }
        return vodPlayFrom.components(separatedBy: "$$$")
    }
    
    var downloadFromSources: [String] {
        guard let vodDownFrom = vodDownFrom, !vodDownFrom.isEmpty else { return [] }
        return vodDownFrom.components(separatedBy: "$$$")
    }
    
    private func parsePlayUrls(_ urlString: String) -> [PlayUrl] {
        let groups = urlString.components(separatedBy: "$$$")
        return groups.enumerated().compactMap { index, group in
            let episodes = group.components(separatedBy: "#")
            let validEpisodes = episodes.compactMap { episode -> Episode? in
                let parts = episode.components(separatedBy: "$")
                guard parts.count >= 2 else { return nil }
                return Episode(name: parts[0], url: parts[1])
            }
            
            guard !validEpisodes.isEmpty else { return nil }
            
            let sourceName = playFromSources.count > index ? playFromSources[index] : "播放源\(index + 1)"
            return PlayUrl(source: sourceName, episodes: validEpisodes)
        }
    }
}

// MARK: - Play URL
struct PlayUrl: Codable, Identifiable, Hashable {
    let id = UUID()
    let source: String
    let episodes: [Episode]
    
    enum CodingKeys: String, CodingKey {
        case source, episodes
    }
    
    var episodeCount: Int {
        return episodes.count
    }
    
    var isMultiEpisode: Bool {
        return episodes.count > 1
    }
    
    var firstEpisode: Episode? {
        return episodes.first
    }
}

// MARK: - Episode
struct Episode: Codable, Identifiable, Hashable {
    let id = UUID()
    let name: String
    let url: String
    
    enum CodingKeys: String, CodingKey {
        case name, url
    }
    
    var isValid: Bool {
        return !name.isEmpty && !url.isEmpty && url.isValidURL
    }
    
    var playURL: URL? {
        return URL(string: url)
    }
}

// MARK: - Video Category
struct VideoCategory: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let parentId: Int?
    
    var isTopLevel: Bool {
        return parentId == nil || parentId == 0
    }
}

// MARK: - Video List Response
struct VideoListResponse: Codable {
    let code: Int
    let msg: String?
    let page: Int?
    let pagecount: Int?
    let limit: String?
    let total: Int?
    let list: [VideoContent]
    let classData: [VideoCategory]?
    
    enum CodingKeys: String, CodingKey {
        case code, msg, page, pagecount, limit, total, list
        case classData = "class"
    }
    
    var isSuccess: Bool {
        return code == 1
    }
    
    var hasMorePages: Bool {
        guard let page = page, let pagecount = pagecount else { return false }
        return page < pagecount
    }
    
    var currentPage: Int {
        return page ?? 1
    }
    
    var totalPages: Int {
        return pagecount ?? 1
    }
    
    var totalCount: Int {
        return total ?? list.count
    }
}

// MARK: - Video Detail Response
struct VideoDetailResponse: Codable {
    let code: Int
    let msg: String?
    let page: Int?
    let pagecount: Int?
    let limit: String?
    let total: Int?
    let list: [VideoContent]
    
    var isSuccess: Bool {
        return code == 1
    }
    
    var videoDetail: VideoContent? {
        return list.first
    }
}

// MARK: - Search Response
struct SearchResponse: Codable {
    let code: Int
    let msg: String?
    let page: Int?
    let pagecount: Int?
    let limit: String?
    let total: Int?
    let list: [VideoContent]
    
    var isSuccess: Bool {
        return code == 1
    }
    
    var hasResults: Bool {
        return !list.isEmpty
    }
    
    var resultCount: Int {
        return list.count
    }
}

// MARK: - Video Filter
struct VideoFilter {
    let category: String?
    let area: String?
    let year: String?
    let lang: String?
    let letter: String?
    let by: String?
    let sort: String?
    
    static let empty = VideoFilter(
        category: nil,
        area: nil,
        year: nil,
        lang: nil,
        letter: nil,
        by: nil,
        sort: nil
    )
    
    func toQueryItems() -> [URLQueryItem] {
        var items: [URLQueryItem] = []
        
        if let category = category { items.append(URLQueryItem(name: "t", value: category)) }
        if let area = area { items.append(URLQueryItem(name: "area", value: area)) }
        if let year = year { items.append(URLQueryItem(name: "year", value: year)) }
        if let lang = lang { items.append(URLQueryItem(name: "lang", value: lang)) }
        if let letter = letter { items.append(URLQueryItem(name: "letter", value: letter)) }
        if let by = by { items.append(URLQueryItem(name: "by", value: by)) }
        if let sort = sort { items.append(URLQueryItem(name: "sort", value: sort)) }
        
        return items
    }
}

// MARK: - Video Content Extensions
extension VideoContent {
    static let preview = VideoContent(
        id: "1",
        name: "示例电影",
        type: "电影",
        pic: "https://example.com/poster.jpg",
        lang: "国语",
        area: "中国大陆",
        year: "2023",
        note: "HD",
        actor: "演员A,演员B",
        director: "导演A",
        des: "这是一部精彩的电影...",
        last: "2023-01-01",
        dt: "2023-01-01",
        tid: 1,
        typeName: "动作片",
        state: 1,
        remarks: "完结",
        serial: 1,
        doubanScore: "8.5",
        vodPlayFrom: "播放源1$$$播放源2",
        vodPlayUrl: "第1集$https://example.com/ep1.m3u8#第2集$https://example.com/ep2.m3u8$$$第1集$https://example2.com/ep1.m3u8",
        vodDownFrom: nil,
        vodDownUrl: nil
    )
}