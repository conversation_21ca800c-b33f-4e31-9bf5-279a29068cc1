//
//  DataSource.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import Foundation

// MARK: - Data Source
struct DataSource: Codable, Identifiable, Hashable {
    let id = UUID()
    let key: String
    let name: String
    let api: String
    let type: DataSourceType
    let searchable: Bool
    let filterable: Bool
    let visible: Bool
    let playerUrl: String?
    let ext: [String: String]?
    let jar: String?
    let categories: [String]?
    let playerType: PlayerType?
    let style: DataSourceStyle?
    
    enum CodingKeys: String, CodingKey {
        case key, name, api, type, searchable, filterable, visible
        case playerUrl = "playUrl"
        case ext, jar, categories, playerType, style
    }
    
    init(
        key: String,
        name: String,
        api: String,
        type: DataSourceType,
        searchable: Bool = true,
        filterable: Bool = true,
        visible: Bool = true,
        playerUrl: String? = nil,
        ext: [String: String]? = nil,
        jar: String? = nil,
        categories: [String]? = nil,
        playerType: PlayerType? = nil,
        style: DataSourceStyle? = nil
    ) {
        self.key = key
        self.name = name
        self.api = api
        self.type = type
        self.searchable = searchable
        self.filterable = filterable
        self.visible = visible
        self.playerUrl = playerUrl
        self.ext = ext
        self.jar = jar
        self.categories = categories
        self.playerType = playerType
        self.style = style
    }
}

// MARK: - Data Source Type
enum DataSourceType: String, Codable, CaseIterable {
    case xml = "0"
    case json = "1"
    case spider = "3"
    case live = "live"
    
    var displayName: String {
        switch self {
        case .xml: return "XML"
        case .json: return "JSON"
        case .spider: return "Spider"
        case .live: return "Live"
        }
    }
    
    var description: String {
        switch self {
        case .xml: return "XML格式数据源"
        case .json: return "JSON格式数据源"
        case .spider: return "爬虫数据源"
        case .live: return "直播数据源"
        }
    }
}

// MARK: - Player Type
enum PlayerType: String, Codable, CaseIterable {
    case system = "0"
    case exo = "1"
    case ijk = "2"
    case external = "3"
    
    var displayName: String {
        switch self {
        case .system: return "系统播放器"
        case .exo: return "ExoPlayer"
        case .ijk: return "IJKPlayer"
        case .external: return "外部播放器"
        }
    }
}

// MARK: - Data Source Style
struct DataSourceStyle: Codable, Hashable {
    let type: StyleType?
    let ratio: Double?
    
    enum StyleType: String, Codable {
        case rect = "rect"
        case oval = "oval"
    }
}

// MARK: - Data Source Configuration
struct DataSourceConfiguration: Codable {
    let sources: [DataSource]
    let lives: [LiveChannelGroup]?
    let parses: [ParseRule]?
    let flags: [String]?
    let ijk: [IJKOption]?
    let ads: [String]?
    let wallpaper: String?
    let spider: String?
    
    var validSources: [DataSource] {
        return sources.filter { $0.visible }
    }
    
    var searchableSources: [DataSource] {
        return sources.filter { $0.searchable && $0.visible }
    }
}

// MARK: - Parse Rule
struct ParseRule: Codable, Identifiable, Hashable {
    let id = UUID()
    let name: String
    let url: String
    let type: ParseType
    let ext: [String: String]?
    let header: [String: String]?
    
    enum CodingKeys: String, CodingKey {
        case name, url, type, ext, header
    }
    
    enum ParseType: Int, Codable {
        case web = 0
        case json = 1
        case mixed = 2
    }
}

// MARK: - Live Channel Group
struct LiveChannelGroup: Codable, Identifiable, Hashable {
    let id = UUID()
    let group: String
    let channels: [LiveChannel]
    
    enum CodingKeys: String, CodingKey {
        case group, channels
    }
}

// MARK: - Live Channel
struct LiveChannel: Codable, Identifiable, Hashable {
    let id = UUID()
    let name: String
    let urls: [String]
    let logo: String?
    let epg: String?
    
    enum CodingKeys: String, CodingKey {
        case name, urls, logo, epg
    }
    
    var primaryUrl: String? {
        return urls.first
    }
    
    var hasMultipleUrls: Bool {
        return urls.count > 1
    }
}

// MARK: - IJK Option
struct IJKOption: Codable, Hashable {
    let category: Int
    let name: String
    let value: String
}

// MARK: - Data Source Extensions
extension DataSource {
    var isValid: Bool {
        return !key.isEmpty && !name.isEmpty && !api.isEmpty && api.isValidURL
    }
    
    var apiURL: URL? {
        return URL(string: api)
    }
    
    var playerURL: URL? {
        guard let playerUrl = playerUrl else { return nil }
        return URL(string: playerUrl)
    }
    
    var jarURL: URL? {
        guard let jar = jar else { return nil }
        return URL(string: jar)
    }
    
    func supports(category: String) -> Bool {
        guard let categories = categories else { return true }
        return categories.contains(category)
    }
}

extension DataSourceConfiguration {
    static let empty = DataSourceConfiguration(
        sources: [],
        lives: nil,
        parses: nil,
        flags: nil,
        ijk: nil,
        ads: nil,
        wallpaper: nil,
        spider: nil
    )
    
    var hasLiveChannels: Bool {
        return lives?.isEmpty == false
    }
    
    var hasParseRules: Bool {
        return parses?.isEmpty == false
    }
    
    func source(withKey key: String) -> DataSource? {
        return sources.first { $0.key == key }
    }
    
    func parseRule(withName name: String) -> ParseRule? {
        return parses?.first { $0.name == name }
    }
}