//
//  VideoViewModel.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Video List View Model
class VideoListViewModel: ObservableObject {
    @Published var videos: [VideoContent] = []
    @Published var categories: [VideoCategory] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var error: AppError?
    @Published var currentPage = 1
    @Published var hasMorePages = false
    @Published var selectedCategory: VideoCategory?
    @Published var currentFilter = VideoFilter.empty
    
    private let videoService: VideoServiceProtocol
    private var currentSource: DataSource?
    private var cancellables = Set<AnyCancellable>()
    
    init(videoService: VideoServiceProtocol = VideoService()) {
        self.videoService = videoService
    }
    
    // MARK: - Public Methods
    func loadVideos(from source: DataSource, filter: VideoFilter = .empty) {
        guard !isLoading else { return }
        
        currentSource = source
        currentFilter = filter
        currentPage = 1
        videos = []
        
        isLoading = true
        error = nil
        
        videoService.getVideoList(from: source, page: currentPage, filter: filter)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        self?.videos = response.list
                        self?.hasMorePages = response.hasMorePages
                        self?.currentPage = response.currentPage
                    } else {
                        self?.error = AppError.data(.invalidResponse)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func loadMoreVideos() {
        guard let source = currentSource,
              hasMorePages,
              !isLoadingMore else { return }
        
        isLoadingMore = true
        let nextPage = currentPage + 1
        
        videoService.getVideoList(from: source, page: nextPage, filter: currentFilter)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoadingMore = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        self?.videos.append(contentsOf: response.list)
                        self?.hasMorePages = response.hasMorePages
                        self?.currentPage = response.currentPage
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func loadCategories(from source: DataSource) {
        videoService.getCategories(from: source)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] categories in
                    self?.categories = categories
                }
            )
            .store(in: &cancellables)
    }
    
    func selectCategory(_ category: VideoCategory?) {
        selectedCategory = category
        
        guard let source = currentSource else { return }
        
        let filter = VideoFilter(
            category: category?.id.description,
            area: currentFilter.area,
            year: currentFilter.year,
            lang: currentFilter.lang,
            letter: currentFilter.letter,
            by: currentFilter.by,
            sort: currentFilter.sort
        )
        
        loadVideos(from: source, filter: filter)
    }
    
    func applyFilter(_ filter: VideoFilter) {
        guard let source = currentSource else { return }
        loadVideos(from: source, filter: filter)
    }
    
    func refreshVideos() {
        guard let source = currentSource else { return }
        loadVideos(from: source, filter: currentFilter)
    }
    
    // MARK: - Computed Properties
    var isEmpty: Bool {
        return videos.isEmpty && !isLoading
    }
    
    var topLevelCategories: [VideoCategory] {
        return categories.filter { $0.isTopLevel }
    }
    
    var videoCount: Int {
        return videos.count
    }
}

// MARK: - Video Detail View Model
class VideoDetailViewModel: ObservableObject {
    @Published var video: VideoContent?
    @Published var isLoading = false
    @Published var error: AppError?
    @Published var selectedPlayUrl: PlayUrl?
    @Published var selectedEpisode: Episode?
    @Published var isFavorite = false
    
    private let videoService: VideoServiceProtocol
    private let favoritesService = FavoritesService.shared
    private let historyService = HistoryService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init(videoService: VideoServiceProtocol = VideoService()) {
        self.videoService = videoService
    }
    
    // MARK: - Public Methods
    func loadVideoDetail(from source: DataSource, videoId: String) {
        isLoading = true
        error = nil
        
        videoService.getVideoDetail(from: source, videoId: videoId)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess, let video = response.videoDetail {
                        self?.video = video
                        self?.selectedPlayUrl = video.playUrls.first
                        self?.selectedEpisode = self?.selectedPlayUrl?.firstEpisode
                        self?.checkFavoriteStatus()
                    } else {
                        self?.error = AppError.data(.notFound)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func setVideo(_ video: VideoContent) {
        self.video = video
        self.selectedPlayUrl = video.playUrls.first
        self.selectedEpisode = selectedPlayUrl?.firstEpisode
        checkFavoriteStatus()
    }
    
    func selectPlayUrl(_ playUrl: PlayUrl) {
        selectedPlayUrl = playUrl
        selectedEpisode = playUrl.firstEpisode
    }
    
    func selectEpisode(_ episode: Episode) {
        selectedEpisode = episode
        
        // Add to history
        if let video = video {
            historyService.addToHistory(video, episode: episode)
        }
    }
    
    func toggleFavorite() {
        guard let video = video else { return }
        
        if isFavorite {
            favoritesService.removeFromFavorites(video) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self?.isFavorite = false
                    case .failure(let error):
                        self?.error = error
                    }
                }
            }
        } else {
            favoritesService.addToFavorites(video) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self?.isFavorite = true
                    case .failure(let error):
                        self?.error = error
                    }
                }
            }
        }
    }
    
    // MARK: - Private Methods
    private func checkFavoriteStatus() {
        guard let video = video else { return }
        
        favoritesService.isFavorite(video) { [weak self] isFavorite in
            DispatchQueue.main.async {
                self?.isFavorite = isFavorite
            }
        }
    }
    
    // MARK: - Computed Properties
    var hasMultiplePlayUrls: Bool {
        return video?.playUrls.count ?? 0 > 1
    }
    
    var hasMultipleEpisodes: Bool {
        return selectedPlayUrl?.isMultiEpisode ?? false
    }
    
    var canPlay: Bool {
        return selectedEpisode?.isValid ?? false
    }
    
    var playUrlNames: [String] {
        return video?.playUrls.map { $0.source } ?? []
    }
    
    var currentEpisodes: [Episode] {
        return selectedPlayUrl?.episodes ?? []
    }
}

// MARK: - Search View Model
class SearchViewModel: ObservableObject {
    @Published var searchQuery = ""
    @Published var searchResults: [VideoContent] = []
    @Published var isSearching = false
    @Published var error: AppError?
    @Published var currentPage = 1
    @Published var hasMoreResults = false
    @Published var searchHistory: [String] = []
    
    private let videoService: VideoServiceProtocol
    private var currentSource: DataSource?
    private var cancellables = Set<AnyCancellable>()
    private let maxHistoryItems = 20
    
    init(videoService: VideoServiceProtocol = VideoService()) {
        self.videoService = videoService
        loadSearchHistory()
        setupSearchBinding()
    }
    
    // MARK: - Public Methods
    func search(in source: DataSource, query: String) {
        guard !query.trimmed.isEmpty else {
            clearResults()
            return
        }
        
        currentSource = source
        searchQuery = query
        currentPage = 1
        searchResults = []
        
        performSearch()
        addToSearchHistory(query)
    }
    
    func loadMoreResults() {
        guard let source = currentSource,
              hasMoreResults,
              !isSearching else { return }
        
        currentPage += 1
        performSearch(isLoadingMore: true)
    }
    
    func clearResults() {
        searchResults = []
        hasMoreResults = false
        currentPage = 1
        error = nil
    }
    
    func clearSearchHistory() {
        searchHistory = []
        saveSearchHistory()
    }
    
    func removeFromSearchHistory(_ query: String) {
        searchHistory.removeAll { $0 == query }
        saveSearchHistory()
    }
    
    // MARK: - Private Methods
    private func setupSearchBinding() {
        $searchQuery
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] query in
                if query.trimmed.isEmpty {
                    self?.clearResults()
                }
            }
            .store(in: &cancellables)
    }
    
    private func performSearch(isLoadingMore: Bool = false) {
        guard let source = currentSource,
              !searchQuery.trimmed.isEmpty else { return }
        
        isSearching = true
        error = nil
        
        videoService.searchVideos(from: source, query: searchQuery, page: currentPage)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isSearching = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] response in
                    if response.isSuccess {
                        if isLoadingMore {
                            self?.searchResults.append(contentsOf: response.list)
                        } else {
                            self?.searchResults = response.list
                        }
                        // Note: Search API might not provide pagination info
                        self?.hasMoreResults = response.list.count >= 20 // Assume page size is 20
                    } else {
                        self?.error = AppError.data(.notFound)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    private func addToSearchHistory(_ query: String) {
        let trimmedQuery = query.trimmed
        guard !trimmedQuery.isEmpty else { return }
        
        // Remove existing entry if present
        searchHistory.removeAll { $0 == trimmedQuery }
        
        // Add to beginning
        searchHistory.insert(trimmedQuery, at: 0)
        
        // Limit history size
        if searchHistory.count > maxHistoryItems {
            searchHistory = Array(searchHistory.prefix(maxHistoryItems))
        }
        
        saveSearchHistory()
    }
    
    private func loadSearchHistory() {
        if let history = UserDefaults.standard.array(forKey: "SearchHistory") as? [String] {
            searchHistory = history
        }
    }
    
    private func saveSearchHistory() {
        UserDefaults.standard.set(searchHistory, forKey: "SearchHistory")
    }
    
    // MARK: - Computed Properties
    var hasResults: Bool {
        return !searchResults.isEmpty
    }
    
    var isEmpty: Bool {
        return searchResults.isEmpty && !isSearching
    }
    
    var resultCount: Int {
        return searchResults.count
    }
    
    var hasSearchHistory: Bool {
        return !searchHistory.isEmpty
    }
}

// MARK: - History View Model
class HistoryViewModel: ObservableObject {
    @Published var historyItems: [HistoryItem] = []
    @Published var isLoading = false
    @Published var error: AppError?
    
    private let historyService = HistoryService.shared
    
    init() {
        loadHistory()
    }
    
    func loadHistory() {
        isLoading = true
        error = nil
        
        historyService.loadHistory { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                switch result {
                case .success(let items):
                    self?.historyItems = items
                case .failure(let error):
                    self?.error = error
                }
            }
        }
    }
    
    func clearHistory() {
        historyService.clearHistory { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self?.historyItems = []
                case .failure(let error):
                    self?.error = error
                }
            }
        }
    }
    
    var isEmpty: Bool {
        return historyItems.isEmpty && !isLoading
    }
    
    var itemCount: Int {
        return historyItems.count
    }
}

// MARK: - Favorites View Model
class FavoritesViewModel: ObservableObject {
    @Published var favoriteVideos: [VideoContent] = []
    @Published var isLoading = false
    @Published var error: AppError?
    
    private let favoritesService = FavoritesService.shared
    
    init() {
        loadFavorites()
    }
    
    func loadFavorites() {
        isLoading = true
        error = nil
        
        favoritesService.loadFavorites { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                switch result {
                case .success(let videos):
                    self?.favoriteVideos = videos
                case .failure(let error):
                    self?.error = error
                }
            }
        }
    }
    
    func removeFavorite(_ video: VideoContent) {
        favoritesService.removeFromFavorites(video) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self?.favoriteVideos.removeAll { $0.id == video.id }
                case .failure(let error):
                    self?.error = error
                }
            }
        }
    }
    
    var isEmpty: Bool {
        return favoriteVideos.isEmpty && !isLoading
    }
    
    var itemCount: Int {
        return favoriteVideos.count
    }
}