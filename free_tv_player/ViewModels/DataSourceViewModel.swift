//
//  DataSourceViewModel.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Data Source View Model
class DataSourceViewModel: ObservableObject {
    @Published var configuration: DataSourceConfiguration = .empty
    @Published var isLoading = false
    @Published var error: AppError?
    @Published var configurationURL = ""
    @Published var isConfigurationValid = false
    
    private let dataSourceService: DataSourceServiceProtocol
    private let userPreferences = UserPreferences.shared
    private var cancellables = Set<AnyCancellable>()
    
    init(dataSourceService: DataSourceServiceProtocol = DataSourceService()) {
        self.dataSourceService = dataSourceService
        setupBindings()
        loadSavedConfiguration()
    }
    
    // MARK: - Public Methods
    func loadConfiguration(from url: String) {
        guard !url.trimmed.isEmpty else {
            error = AppError.url(.invalidURL)
            return
        }
        
        isLoading = true
        error = nil
        
        dataSourceService.loadConfiguration(from: url)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] config in
                    self?.configuration = config
                    self?.configurationURL = url
                    self?.userPreferences.lastConfigurationURL = url
                    self?.saveConfiguration()
                }
            )
            .store(in: &cancellables)
    }
    
    func refreshConfiguration() {
        guard let lastURL = userPreferences.lastConfigurationURL else {
            error = AppError.configuration(.notFound)
            return
        }
        
        loadConfiguration(from: lastURL)
    }
    
    func validateConfigurationURL(_ url: String) -> Bool {
        return url.isValidURL
    }
    
    func clearConfiguration() {
        configuration = .empty
        configurationURL = ""
        userPreferences.lastConfigurationURL = nil
        
        // Clear saved configuration
        dataSourceService.saveConfiguration(.empty)
            .sink(
                receiveCompletion: { _ in },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Computed Properties
    var validSources: [DataSource] {
        return configuration.validSources
    }
    
    var searchableSources: [DataSource] {
        return configuration.searchableSources
    }
    
    var hasLiveChannels: Bool {
        return configuration.hasLiveChannels
    }
    
    var hasParseRules: Bool {
        return configuration.hasParseRules
    }
    
    var sourceCount: Int {
        return configuration.sources.count
    }
    
    var liveChannelCount: Int {
        return configuration.lives?.reduce(0) { $0 + $1.channels.count } ?? 0
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        $configurationURL
            .map { [weak self] url in
                self?.validateConfigurationURL(url) ?? false
            }
            .assign(to: &$isConfigurationValid)
    }
    
    private func loadSavedConfiguration() {
        dataSourceService.loadSavedConfiguration()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] config in
                    if let config = config {
                        self?.configuration = config
                    }
                    
                    // Load last configuration URL
                    if let lastURL = self?.userPreferences.lastConfigurationURL {
                        self?.configurationURL = lastURL
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    private func saveConfiguration() {
        dataSourceService.saveConfiguration(configuration)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { _ in
                    // Configuration saved successfully
                }
            )
            .store(in: &cancellables)
    }
}

// MARK: - Data Source Selection View Model
class DataSourceSelectionViewModel: ObservableObject {
    @Published var selectedSource: DataSource?
    @Published var availableSources: [DataSource] = []
    
    private let configuration: DataSourceConfiguration
    
    init(configuration: DataSourceConfiguration) {
        self.configuration = configuration
        self.availableSources = configuration.validSources
        self.selectedSource = availableSources.first
    }
    
    func selectSource(_ source: DataSource) {
        selectedSource = source
    }
    
    func selectSource(withKey key: String) {
        selectedSource = availableSources.first { $0.key == key }
    }
    
    var hasMultipleSources: Bool {
        return availableSources.count > 1
    }
    
    var sourceNames: [String] {
        return availableSources.map { $0.name }
    }
}

// MARK: - Live Channel View Model
class LiveChannelViewModel: ObservableObject {
    @Published var channelGroups: [LiveChannelGroup] = []
    @Published var selectedGroup: LiveChannelGroup?
    @Published var selectedChannel: LiveChannel?
    @Published var isLoading = false
    @Published var error: AppError?
    
    private let configuration: DataSourceConfiguration
    
    init(configuration: DataSourceConfiguration) {
        self.configuration = configuration
        self.channelGroups = configuration.lives ?? []
        self.selectedGroup = channelGroups.first
    }
    
    func selectGroup(_ group: LiveChannelGroup) {
        selectedGroup = group
        selectedChannel = nil
    }
    
    func selectChannel(_ channel: LiveChannel) {
        selectedChannel = channel
    }
    
    var currentChannels: [LiveChannel] {
        return selectedGroup?.channels ?? []
    }
    
    var groupNames: [String] {
        return channelGroups.map { $0.group }
    }
    
    var totalChannelCount: Int {
        return channelGroups.reduce(0) { $0 + $1.channels.count }
    }
    
    var hasChannels: Bool {
        return !channelGroups.isEmpty
    }
}

// MARK: - Configuration Import View Model
class ConfigurationImportViewModel: ObservableObject {
    @Published var importURL = ""
    @Published var isImporting = false
    @Published var importError: AppError?
    @Published var importSuccess = false
    @Published var previewConfiguration: DataSourceConfiguration?
    
    private let dataSourceService: DataSourceServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    
    init(dataSourceService: DataSourceServiceProtocol = DataSourceService()) {
        self.dataSourceService = dataSourceService
    }
    
    func previewConfiguration(from url: String) {
        guard url.isValidURL else {
            importError = AppError.url(.invalidURL)
            return
        }
        
        isImporting = true
        importError = nil
        
        dataSourceService.loadConfiguration(from: url)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isImporting = false
                    if case .failure(let error) = completion {
                        self?.importError = error
                    }
                },
                receiveValue: { [weak self] config in
                    self?.previewConfiguration = config
                }
            )
            .store(in: &cancellables)
    }
    
    func importConfiguration() {
        guard let config = previewConfiguration else {
            importError = AppError.configuration(.notFound)
            return
        }
        
        isImporting = true
        importError = nil
        
        dataSourceService.saveConfiguration(config)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isImporting = false
                    if case .failure(let error) = completion {
                        self?.importError = error
                    }
                },
                receiveValue: { [weak self] _ in
                    self?.importSuccess = true
                    UserPreferences.shared.lastConfigurationURL = self?.importURL
                }
            )
            .store(in: &cancellables)
    }
    
    func resetImport() {
        importURL = ""
        importError = nil
        importSuccess = false
        previewConfiguration = nil
    }
    
    var canPreview: Bool {
        return importURL.isValidURL && !isImporting
    }
    
    var canImport: Bool {
        return previewConfiguration != nil && !isImporting
    }
    
    var previewSourceCount: Int {
        return previewConfiguration?.sources.count ?? 0
    }
    
    var previewLiveChannelCount: Int {
        return previewConfiguration?.lives?.reduce(0) { $0 + $1.channels.count } ?? 0
    }
}