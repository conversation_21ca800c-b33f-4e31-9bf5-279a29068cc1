//
//  DataSourceService.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Data Source Service Protocol
protocol DataSourceServiceProtocol {
    func loadConfiguration(from url: String) -> AnyPublisher<DataSourceConfiguration, AppError>
    func loadConfiguration(from data: Data) -> AnyPublisher<DataSourceConfiguration, AppError>
    func validateConfiguration(_ config: DataSourceConfiguration) -> Bool
    func saveConfiguration(_ config: DataSourceConfiguration) -> AnyPublisher<Void, AppError>
    func loadSavedConfiguration() -> AnyPublisher<DataSourceConfiguration?, AppError>
}

// MARK: - Data Source Service Implementation
class DataSourceService: DataSourceServiceProtocol {
    private let networkManager: NetworkManagerProtocol
    private let storageService: StorageServiceProtocol
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder
    
    init(
        networkManager: NetworkManagerProtocol = NetworkManager.shared,
        storageService: StorageServiceProtocol = StorageService.shared
    ) {
        self.networkManager = networkManager
        self.storageService = storageService
        
        self.decoder = JSONDecoder()
        self.decoder.dateDecodingStrategy = .iso8601
        
        self.encoder = JSONEncoder()
        self.encoder.dateEncodingStrategy = .iso8601
        self.encoder.outputFormatting = .prettyPrinted
    }
    
    func loadConfiguration(from url: String) -> AnyPublisher<DataSourceConfiguration, AppError> {
        guard let configURL = URL(string: url) else {
            return Fail(error: AppError.url(.invalidURL))
                .eraseToAnyPublisher()
        }
        
        return networkManager.downloadData(from: configURL)
            .flatMap { [weak self] data -> AnyPublisher<DataSourceConfiguration, AppError> in
                guard let self = self else {
                    return Fail(error: AppError.data(.processingFailed))
                        .eraseToAnyPublisher()
                }
                return self.loadConfiguration(from: data)
            }
            .eraseToAnyPublisher()
    }
    
    func loadConfiguration(from data: Data) -> AnyPublisher<DataSourceConfiguration, AppError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AppError.data(.processingFailed)))
                return
            }
            
            do {
                // Try to decode as JSON first
                let config = try self.decoder.decode(DataSourceConfiguration.self, from: data)
                
                // Validate configuration
                if self.validateConfiguration(config) {
                    promise(.success(config))
                } else {
                    promise(.failure(AppError.configuration(.invalidFormat)))
                }
            } catch {
                // If JSON decoding fails, try other formats
                self.parseAlternativeFormats(data: data) { result in
                    promise(result)
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func validateConfiguration(_ config: DataSourceConfiguration) -> Bool {
        // Check if configuration has at least one valid source
        guard !config.sources.isEmpty else { return false }
        
        // Validate each source
        for source in config.sources {
            if !source.isValid {
                return false
            }
        }
        
        return true
    }
    
    func saveConfiguration(_ config: DataSourceConfiguration) -> AnyPublisher<Void, AppError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AppError.data(.processingFailed)))
                return
            }
            
            do {
                let data = try self.encoder.encode(config)
                self.storageService.save(data, forKey: AppConstants.Storage.configurationKey) { result in
                    switch result {
                    case .success:
                        promise(.success(()))
                    case .failure(let error):
                        promise(.failure(error))
                    }
                }
            } catch {
                promise(.failure(AppError.data(.encodingFailed)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func loadSavedConfiguration() -> AnyPublisher<DataSourceConfiguration?, AppError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(AppError.data(.processingFailed)))
                return
            }
            
            self.storageService.load(forKey: AppConstants.Storage.configurationKey) { result in
                switch result {
                case .success(let data):
                    if let data = data {
                        do {
                            let config = try self.decoder.decode(DataSourceConfiguration.self, from: data)
                            promise(.success(config))
                        } catch {
                            promise(.failure(AppError.parsing(.invalidFormat)))
                        }
                    } else {
                        promise(.success(nil))
                    }
                case .failure(let error):
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods
    private func parseAlternativeFormats(data: Data, completion: @escaping (Result<DataSourceConfiguration, AppError>) -> Void) {
        // Try to parse as encrypted JSON
        if let decryptedData = decryptData(data) {
            do {
                let config = try decoder.decode(DataSourceConfiguration.self, from: decryptedData)
                if validateConfiguration(config) {
                    completion(.success(config))
                    return
                }
            } catch {
                // Continue to next format
            }
        }
        
        // Try to parse as base64 encoded JSON
        if let base64String = String(data: data, encoding: .utf8),
           let decodedData = Data(base64Encoded: base64String) {
            do {
                let config = try decoder.decode(DataSourceConfiguration.self, from: decodedData)
                if validateConfiguration(config) {
                    completion(.success(config))
                    return
                }
            } catch {
                // Continue to next format
            }
        }
        
        // If all formats fail
        completion(.failure(AppError.parsing(.unsupportedFormat)))
    }
    
    private func decryptData(_ data: Data) -> Data? {
        // Implement decryption logic here if needed
        // This is a placeholder for potential encryption support
        return nil
    }
}

// MARK: - Configuration Validator
struct ConfigurationValidator {
    static func validate(_ config: DataSourceConfiguration) -> [ValidationError] {
        var errors: [ValidationError] = []
        
        // Check sources
        if config.sources.isEmpty {
            errors.append(.noSources)
        }
        
        for (index, source) in config.sources.enumerated() {
            if source.key.isEmpty {
                errors.append(.invalidSource(index: index, reason: "Key is empty"))
            }
            
            if source.name.isEmpty {
                errors.append(.invalidSource(index: index, reason: "Name is empty"))
            }
            
            if !source.api.isValidURL {
                errors.append(.invalidSource(index: index, reason: "Invalid API URL"))
            }
        }
        
        // Check parse rules
        if let parses = config.parses {
            for (index, parse) in parses.enumerated() {
                if parse.name.isEmpty {
                    errors.append(.invalidParseRule(index: index, reason: "Name is empty"))
                }
                
                if !parse.url.isValidURL {
                    errors.append(.invalidParseRule(index: index, reason: "Invalid URL"))
                }
            }
        }
        
        // Check live channels
        if let lives = config.lives {
            for (groupIndex, group) in lives.enumerated() {
                if group.group.isEmpty {
                    errors.append(.invalidLiveGroup(index: groupIndex, reason: "Group name is empty"))
                }
                
                for (channelIndex, channel) in group.channels.enumerated() {
                    if channel.name.isEmpty {
                        errors.append(.invalidLiveChannel(
                            groupIndex: groupIndex,
                            channelIndex: channelIndex,
                            reason: "Channel name is empty"
                        ))
                    }
                    
                    if channel.urls.isEmpty {
                        errors.append(.invalidLiveChannel(
                            groupIndex: groupIndex,
                            channelIndex: channelIndex,
                            reason: "No URLs provided"
                        ))
                    }
                }
            }
        }
        
        return errors
    }
}

// MARK: - Validation Error
enum ValidationError: Error, LocalizedError {
    case noSources
    case invalidSource(index: Int, reason: String)
    case invalidParseRule(index: Int, reason: String)
    case invalidLiveGroup(index: Int, reason: String)
    case invalidLiveChannel(groupIndex: Int, channelIndex: Int, reason: String)
    
    var errorDescription: String? {
        switch self {
        case .noSources:
            return "配置中没有数据源"
        case .invalidSource(let index, let reason):
            return "数据源 \(index) 无效: \(reason)"
        case .invalidParseRule(let index, let reason):
            return "解析规则 \(index) 无效: \(reason)"
        case .invalidLiveGroup(let index, let reason):
            return "直播分组 \(index) 无效: \(reason)"
        case .invalidLiveChannel(let groupIndex, let channelIndex, let reason):
            return "直播频道 \(groupIndex)-\(channelIndex) 无效: \(reason)"
        }
    }
}