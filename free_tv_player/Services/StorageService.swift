//
//  StorageService.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Storage Service Protocol
protocol StorageServiceProtocol {
    func save(_ data: Data, forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void)
    func load(forKey key: String, completion: @escaping (Result<Data?, AppError>) -> Void)
    func delete(forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void)
    func exists(forKey key: String) -> Bool
    func clearAll(completion: @escaping (Result<Void, AppError>) -> Void)
    func getStorageSize() -> Int64
}

// MARK: - Storage Service
class StorageService: StorageServiceProtocol, ObservableObject {
    static let shared = StorageService()
    
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    private let cacheDirectory: URL
    private let queue = DispatchQueue(label: "StorageService", qos: .utility)
    
    private init() {
        self.documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        
        createDirectoriesIfNeeded()
    }
    
    func save(_ data: Data, forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void) {
        queue.async { [weak self] in
            guard let self = self else {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.saveFailed)))
                }
                return
            }
            
            do {
                let url = self.fileURL(for: key)
                try data.write(to: url)
                
                DispatchQueue.main.async {
                    completion(.success(()))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.saveFailed)))
                }
            }
        }
    }
    
    func load(forKey key: String, completion: @escaping (Result<Data?, AppError>) -> Void) {
        queue.async { [weak self] in
            guard let self = self else {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.loadFailed)))
                }
                return
            }
            
            let url = self.fileURL(for: key)
            
            guard self.fileManager.fileExists(atPath: url.path) else {
                DispatchQueue.main.async {
                    completion(.success(nil))
                }
                return
            }
            
            do {
                let data = try Data(contentsOf: url)
                DispatchQueue.main.async {
                    completion(.success(data))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.loadFailed)))
                }
            }
        }
    }
    
    func delete(forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void) {
        queue.async { [weak self] in
            guard let self = self else {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.deleteFailed)))
                }
                return
            }
            
            let url = self.fileURL(for: key)
            
            guard self.fileManager.fileExists(atPath: url.path) else {
                DispatchQueue.main.async {
                    completion(.success(()))
                }
                return
            }
            
            do {
                try self.fileManager.removeItem(at: url)
                DispatchQueue.main.async {
                    completion(.success(()))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.deleteFailed)))
                }
            }
        }
    }
    
    func exists(forKey key: String) -> Bool {
        let url = fileURL(for: key)
        return fileManager.fileExists(atPath: url.path)
    }
    
    func clearAll(completion: @escaping (Result<Void, AppError>) -> Void) {
        queue.async { [weak self] in
            guard let self = self else {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.clearFailed)))
                }
                return
            }
            
            do {
                let appDirectory = self.documentsDirectory.appendingPathComponent(AppConstants.App.name)
                if self.fileManager.fileExists(atPath: appDirectory.path) {
                    try self.fileManager.removeItem(at: appDirectory)
                }
                
                self.createDirectoriesIfNeeded()
                
                DispatchQueue.main.async {
                    completion(.success(()))
                }
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(AppError.storage(.clearFailed)))
                }
            }
        }
    }
    
    func getStorageSize() -> Int64 {
        let appDirectory = documentsDirectory.appendingPathComponent(AppConstants.App.name)
        return directorySize(at: appDirectory)
    }
    
    // MARK: - Private Methods
    private func createDirectoriesIfNeeded() {
        let appDirectory = documentsDirectory.appendingPathComponent(AppConstants.App.name)
        let configDirectory = appDirectory.appendingPathComponent("Config")
        let cacheDirectory = appDirectory.appendingPathComponent("Cache")
        let historyDirectory = appDirectory.appendingPathComponent("History")
        let favoritesDirectory = appDirectory.appendingPathComponent("Favorites")
        
        let directories = [appDirectory, configDirectory, cacheDirectory, historyDirectory, favoritesDirectory]
        
        for directory in directories {
            if !fileManager.fileExists(atPath: directory.path) {
                try? fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            }
        }
    }
    
    private func fileURL(for key: String) -> URL {
        let appDirectory = documentsDirectory.appendingPathComponent(AppConstants.App.name)
        
        // Determine subdirectory based on key
        let subdirectory: String
        if key.contains("config") {
            subdirectory = "Config"
        } else if key.contains("cache") {
            subdirectory = "Cache"
        } else if key.contains("history") {
            subdirectory = "History"
        } else if key.contains("favorite") {
            subdirectory = "Favorites"
        } else {
            subdirectory = "Data"
        }
        
        let directory = appDirectory.appendingPathComponent(subdirectory)
        return directory.appendingPathComponent("\(key).data")
    }
    
    private func directorySize(at url: URL) -> Int64 {
        guard let enumerator = fileManager.enumerator(at: url, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize: Int64 = 0
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = resourceValues.fileSize {
                    totalSize += Int64(fileSize)
                }
            } catch {
                continue
            }
        }
        
        return totalSize
    }
}

// MARK: - Cache Manager
class CacheManager {
    static let shared = CacheManager()
    
    private let storageService: StorageServiceProtocol
    private let maxCacheSize: Int64 = 100 * 1024 * 1024 // 100MB
    private let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    private init(storageService: StorageServiceProtocol = StorageService.shared) {
        self.storageService = storageService
    }
    
    func cacheData(_ data: Data, forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void) {
        let cacheKey = "cache_\(key)"
        let metadata = CacheMetadata(key: key, timestamp: Date(), size: Int64(data.count))
        
        // Save metadata
        if let metadataData = try? JSONEncoder().encode(metadata) {
            storageService.save(metadataData, forKey: "\(cacheKey)_metadata") { _ in }
        }
        
        // Save actual data
        storageService.save(data, forKey: cacheKey, completion: completion)
        
        // Clean up old cache if needed
        cleanupCacheIfNeeded()
    }
    
    func loadCachedData(forKey key: String, completion: @escaping (Result<Data?, AppError>) -> Void) {
        let cacheKey = "cache_\(key)"
        
        // Check if cache is still valid
        storageService.load(forKey: "\(cacheKey)_metadata") { [weak self] result in
            switch result {
            case .success(let metadataData):
                if let metadataData = metadataData,
                   let metadata = try? JSONDecoder().decode(CacheMetadata.self, from: metadataData) {
                    
                    let age = Date().timeIntervalSince(metadata.timestamp)
                    if age > self?.maxCacheAge ?? 0 {
                        // Cache expired, delete it
                        self?.deleteCachedData(forKey: key) { _ in }
                        completion(.success(nil))
                        return
                    }
                }
                
                // Load cached data
                self?.storageService.load(forKey: cacheKey, completion: completion)
                
            case .failure:
                completion(.success(nil))
            }
        }
    }
    
    func deleteCachedData(forKey key: String, completion: @escaping (Result<Void, AppError>) -> Void) {
        let cacheKey = "cache_\(key)"
        
        storageService.delete(forKey: cacheKey) { [weak self] result in
            // Also delete metadata
            self?.storageService.delete(forKey: "\(cacheKey)_metadata") { _ in }
            completion(result)
        }
    }
    
    func clearAllCache(completion: @escaping (Result<Void, AppError>) -> Void) {
        // This would require enumerating all cache files
        // For now, we'll use the storage service's clearAll method
        storageService.clearAll(completion: completion)
    }
    
    private func cleanupCacheIfNeeded() {
        let currentSize = storageService.getStorageSize()
        if currentSize > maxCacheSize {
            // Implement LRU cache cleanup logic here
            // This is a simplified version
            clearAllCache { _ in }
        }
    }
}

// MARK: - Cache Metadata
struct CacheMetadata: Codable {
    let key: String
    let timestamp: Date
    let size: Int64
}

// MARK: - User Preferences
class UserPreferences: ObservableObject {
    static let shared = UserPreferences()
    
    private let userDefaults = UserDefaults.standard
    
    private init() {}
    
    // MARK: - Player Settings
    var playerType: PlayerType {
        get {
            let rawValue = userDefaults.string(forKey: AppConstants.Storage.playerTypeKey) ?? PlayerType.system.rawValue
            return PlayerType(rawValue: rawValue) ?? .system
        }
        set {
            userDefaults.set(newValue.rawValue, forKey: AppConstants.Storage.playerTypeKey)
        }
    }
    
    var autoPlay: Bool {
        get { userDefaults.bool(forKey: AppConstants.Storage.autoPlayKey) }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.autoPlayKey) }
    }
    
    var backgroundPlay: Bool {
        get { userDefaults.bool(forKey: AppConstants.Storage.backgroundPlayKey) }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.backgroundPlayKey) }
    }
    
    var landscapeOrientation: Bool {
        get { userDefaults.bool(forKey: "landscapeOrientation") }
        set { userDefaults.set(newValue, forKey: "landscapeOrientation") }
    }
    
    var hardwareDecoding: Bool {
        get { userDefaults.bool(forKey: "hardwareDecoding") }
        set { userDefaults.set(newValue, forKey: "hardwareDecoding") }
    }
    
    // MARK: - UI Settings
    var gridColumns: Int {
        get { 
            let columns = userDefaults.integer(forKey: AppConstants.Storage.gridColumnsKey)
            return columns > 0 ? columns : 2
        }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.gridColumnsKey) }
    }
    
    var showThumbnails: Bool {
        get { userDefaults.bool(forKey: AppConstants.Storage.showThumbnailsKey) }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.showThumbnailsKey) }
    }
    
    var theme: String {
        get { userDefaults.string(forKey: "theme") ?? "system" }
        set { userDefaults.set(newValue, forKey: "theme") }
    }
    
    // MARK: - Network Settings
    var requestTimeout: TimeInterval {
        get { 
            let timeout = userDefaults.double(forKey: "requestTimeout")
            return timeout > 0 ? timeout : 30.0
        }
        set { userDefaults.set(newValue, forKey: "requestTimeout") }
    }
    
    var maxConcurrentDownloads: Int {
        get {
            let count = userDefaults.integer(forKey: "maxConcurrentDownloads")
            return count > 0 ? count : 3
        }
        set { userDefaults.set(newValue, forKey: "maxConcurrentDownloads") }
    }
    
    var useWiFiOnly: Bool {
        get { userDefaults.bool(forKey: "useWiFiOnly") }
        set { userDefaults.set(newValue, forKey: "useWiFiOnly") }
    }
    
    // MARK: - Storage Settings
    var maxCacheSize: Int64 {
        get {
            let size = userDefaults.object(forKey: "maxCacheSize") as? Int64
            return size ?? (500 * 1024 * 1024) // 500MB default
        }
        set { userDefaults.set(newValue, forKey: "maxCacheSize") }
    }
    
    // MARK: - Privacy Settings
    var saveHistory: Bool {
        get { userDefaults.bool(forKey: "saveHistory") }
        set { userDefaults.set(newValue, forKey: "saveHistory") }
    }
    
    var saveFavorites: Bool {
        get { userDefaults.bool(forKey: "saveFavorites") }
        set { userDefaults.set(newValue, forKey: "saveFavorites") }
    }
    
    var enableAnalytics: Bool {
        get { userDefaults.bool(forKey: "enableAnalytics") }
        set { userDefaults.set(newValue, forKey: "enableAnalytics") }
    }
    
    // MARK: - Data Source Settings
    var lastConfigurationURL: String? {
        get { userDefaults.string(forKey: AppConstants.Storage.lastConfigURLKey) }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.lastConfigURLKey) }
    }
    
    var autoUpdateConfiguration: Bool {
        get { userDefaults.bool(forKey: AppConstants.Storage.autoUpdateConfigKey) }
        set { userDefaults.set(newValue, forKey: AppConstants.Storage.autoUpdateConfigKey) }
    }
}