//
//  VideoService.swift
//  FreeTVPlayer
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/27.
//

import Foundation
import Combine

// MARK: - Video Service Protocol
protocol VideoServiceProtocol {
    func getVideoList(
        from source: DataSource,
        page: Int,
        filter: VideoFilter
    ) -> AnyPublisher<VideoListResponse, AppError>
    
    func getVideoDetail(
        from source: DataSource,
        videoId: String
    ) -> AnyPublisher<VideoDetailResponse, AppError>
    
    func searchVideos(
        from source: DataSource,
        query: String,
        page: Int
    ) -> AnyPublisher<SearchResponse, AppError>
    
    func getCategories(
        from source: DataSource
    ) -> AnyPublisher<[VideoCategory], AppError>
}

// MARK: - Video Service Implementation
class VideoService: VideoServiceProtocol {
    private let networkManager: NetworkManagerProtocol
    private let cacheManager: CacheManager
    
    init(
        networkManager: NetworkManagerProtocol = NetworkManager.shared,
        cacheManager: CacheManager = CacheManager.shared
    ) {
        self.networkManager = networkManager
        self.cacheManager = cacheManager
    }
    
    func getVideoList(
        from source: DataSource,
        page: Int = 1,
        filter: VideoFilter = .empty
    ) -> AnyPublisher<VideoListResponse, AppError> {
        let endpoint = buildVideoListEndpoint(source: source, page: page, filter: filter)
        
        return networkManager.request(endpoint, responseType: VideoListResponse.self)
            .handleEvents(receiveOutput: { [weak self] response in
                // Cache successful responses
                if response.isSuccess {
                    let cacheKey = "videolist_\(source.key)_\(page)_\(filter.hashValue)"
                    if let data = try? JSONEncoder().encode(response) {
                        self?.cacheManager.cacheData(data, forKey: cacheKey) { _ in }
                    }
                }
            })
            .eraseToAnyPublisher()
    }
    
    func getVideoDetail(
        from source: DataSource,
        videoId: String
    ) -> AnyPublisher<VideoDetailResponse, AppError> {
        let cacheKey = "videodetail_\(source.key)_\(videoId)"
        
        // Try cache first
        return Future<VideoDetailResponse, AppError> { [weak self] promise in
            self?.cacheManager.loadCachedData(forKey: cacheKey) { result in
                switch result {
                case .success(let data):
                    if let data = data,
                       let response = try? JSONDecoder().decode(VideoDetailResponse.self, from: data) {
                        promise(.success(response))
                    } else {
                        // Cache miss, fetch from network
                        self?.fetchVideoDetailFromNetwork(source: source, videoId: videoId, cacheKey: cacheKey, promise: promise)
                    }
                case .failure:
                    // Cache error, fetch from network
                    self?.fetchVideoDetailFromNetwork(source: source, videoId: videoId, cacheKey: cacheKey, promise: promise)
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func searchVideos(
        from source: DataSource,
        query: String,
        page: Int = 1
    ) -> AnyPublisher<SearchResponse, AppError> {
        guard source.searchable else {
            return Fail(error: AppError.data(.notSupported))
                .eraseToAnyPublisher()
        }
        
        let endpoint = buildSearchEndpoint(source: source, query: query, page: page)
        
        return networkManager.request(endpoint, responseType: SearchResponse.self)
            .eraseToAnyPublisher()
    }
    
    func getCategories(
        from source: DataSource
    ) -> AnyPublisher<[VideoCategory], AppError> {
        let cacheKey = "categories_\(source.key)"
        
        // Try cache first
        return Future<[VideoCategory], AppError> { [weak self] promise in
            self?.cacheManager.loadCachedData(forKey: cacheKey) { result in
                switch result {
                case .success(let data):
                    if let data = data,
                       let categories = try? JSONDecoder().decode([VideoCategory].self, from: data) {
                        promise(.success(categories))
                    } else {
                        // Cache miss, fetch from network
                        self?.fetchCategoriesFromNetwork(source: source, cacheKey: cacheKey, promise: promise)
                    }
                case .failure:
                    // Cache error, fetch from network
                    self?.fetchCategoriesFromNetwork(source: source, cacheKey: cacheKey, promise: promise)
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods
    private func buildVideoListEndpoint(source: DataSource, page: Int, filter: VideoFilter) -> APIEndpoint {
        var queryItems: [URLQueryItem] = [
            URLQueryItem(name: "ac", value: "list"),
            URLQueryItem(name: "pg", value: "\(page)")
        ]
        
        // Add filter parameters
        queryItems.append(contentsOf: filter.toQueryItems())
        
        return APIEndpoint(
            path: source.api,
            method: .GET,
            queryItems: queryItems
        )
    }
    
    private func buildVideoDetailEndpoint(source: DataSource, videoId: String) -> APIEndpoint {
        let queryItems: [URLQueryItem] = [
            URLQueryItem(name: "ac", value: "detail"),
            URLQueryItem(name: "ids", value: videoId)
        ]
        
        return APIEndpoint(
            path: source.api,
            method: .GET,
            queryItems: queryItems
        )
    }
    
    private func buildSearchEndpoint(source: DataSource, query: String, page: Int) -> APIEndpoint {
        let queryItems: [URLQueryItem] = [
            URLQueryItem(name: "ac", value: "list"),
            URLQueryItem(name: "wd", value: query),
            URLQueryItem(name: "pg", value: "\(page)")
        ]
        
        return APIEndpoint(
            path: source.api,
            method: .GET,
            queryItems: queryItems
        )
    }
    
    private func buildCategoriesEndpoint(source: DataSource) -> APIEndpoint {
        let queryItems: [URLQueryItem] = [
            URLQueryItem(name: "ac", value: "list")
        ]
        
        return APIEndpoint(
            path: source.api,
            method: .GET,
            queryItems: queryItems
        )
    }
    
    private func fetchVideoDetailFromNetwork(
        source: DataSource,
        videoId: String,
        cacheKey: String,
        promise: @escaping (Result<VideoDetailResponse, AppError>) -> Void
    ) {
        let endpoint = buildVideoDetailEndpoint(source: source, videoId: videoId)
        
        let cancellable = networkManager.request(endpoint, responseType: VideoDetailResponse.self)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        promise(.failure(error))
                    }
                },
                receiveValue: { [weak self] response in
                    // Cache successful response
                    if response.isSuccess {
                        if let data = try? JSONEncoder().encode(response) {
                            self?.cacheManager.cacheData(data, forKey: cacheKey) { _ in }
                        }
                    }
                    promise(.success(response))
                }
            )
        
        // Store cancellable to prevent deallocation
        // In a real implementation, you'd want to manage these properly
    }
    
    private func fetchCategoriesFromNetwork(
        source: DataSource,
        cacheKey: String,
        promise: @escaping (Result<[VideoCategory], AppError>) -> Void
    ) {
        let endpoint = buildCategoriesEndpoint(source: source)
        
        let cancellable = networkManager.request(endpoint, responseType: VideoListResponse.self)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        promise(.failure(error))
                    }
                },
                receiveValue: { [weak self] response in
                    let categories = response.classData ?? []
                    
                    // Cache categories
                    if let data = try? JSONEncoder().encode(categories) {
                        self?.cacheManager.cacheData(data, forKey: cacheKey) { _ in }
                    }
                    
                    promise(.success(categories))
                }
            )
    }
}

// MARK: - Video Filter Extensions
extension VideoFilter: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(category)
        hasher.combine(area)
        hasher.combine(year)
        hasher.combine(lang)
        hasher.combine(letter)
        hasher.combine(by)
        hasher.combine(sort)
    }
    
    static func == (lhs: VideoFilter, rhs: VideoFilter) -> Bool {
        return lhs.category == rhs.category &&
               lhs.area == rhs.area &&
               lhs.year == rhs.year &&
               lhs.lang == rhs.lang &&
               lhs.letter == rhs.letter &&
               lhs.by == rhs.by &&
               lhs.sort == rhs.sort
    }
}

// MARK: - History Service
class HistoryService {
    static let shared = HistoryService()
    
    private let storageService: StorageServiceProtocol
    private let maxHistoryItems = 1000
    
    private init(storageService: StorageServiceProtocol = StorageService.shared) {
        self.storageService = storageService
    }
    
    func addToHistory(_ video: VideoContent, episode: Episode? = nil) {
        loadHistory { [weak self] result in
            switch result {
            case .success(var history):
                let historyItem = HistoryItem(
                    video: video,
                    episode: episode,
                    watchedAt: Date(),
                    progress: 0
                )
                
                // Remove existing item if present
                history.removeAll { $0.video.id == video.id }
                
                // Add to beginning
                history.insert(historyItem, at: 0)
                
                // Limit history size
                if history.count > self?.maxHistoryItems ?? 1000 {
                    history = Array(history.prefix(self?.maxHistoryItems ?? 1000))
                }
                
                self?.saveHistory(history) { _ in }
                
            case .failure:
                // Create new history with this item
                let historyItem = HistoryItem(
                    video: video,
                    episode: episode,
                    watchedAt: Date(),
                    progress: 0
                )
                self?.saveHistory([historyItem]) { _ in }
            }
        }
    }
    
    func loadHistory(completion: @escaping (Result<[HistoryItem], AppError>) -> Void) {
        storageService.load(forKey: AppConstants.Storage.historyKey) { result in
            switch result {
            case .success(let data):
                if let data = data,
                   let history = try? JSONDecoder().decode([HistoryItem].self, from: data) {
                    completion(.success(history))
                } else {
                    completion(.success([]))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func clearHistory(completion: @escaping (Result<Void, AppError>) -> Void) {
        storageService.delete(forKey: AppConstants.Storage.historyKey, completion: completion)
    }
    
    private func saveHistory(_ history: [HistoryItem], completion: @escaping (Result<Void, AppError>) -> Void) {
        do {
            let data = try JSONEncoder().encode(history)
            storageService.save(data, forKey: AppConstants.Storage.historyKey, completion: completion)
        } catch {
            completion(.failure(AppError.data(.encodingFailed)))
        }
    }
}

// MARK: - History Item
struct HistoryItem: Codable, Identifiable {
    let id = UUID()
    let video: VideoContent
    let episode: Episode?
    let watchedAt: Date
    let progress: Double // 0.0 to 1.0
    
    enum CodingKeys: String, CodingKey {
        case video, episode, watchedAt, progress
    }
}

// MARK: - Favorites Service
class FavoritesService {
    static let shared = FavoritesService()
    
    private let storageService: StorageServiceProtocol
    
    private init(storageService: StorageServiceProtocol = StorageService.shared) {
        self.storageService = storageService
    }
    
    func addToFavorites(_ video: VideoContent, completion: @escaping (Result<Void, AppError>) -> Void) {
        loadFavorites { [weak self] result in
            switch result {
            case .success(var favorites):
                // Check if already in favorites
                if !favorites.contains(where: { $0.id == video.id }) {
                    favorites.append(video)
                    self?.saveFavorites(favorites, completion: completion)
                } else {
                    completion(.success(()))
                }
            case .failure:
                // Create new favorites with this item
                self?.saveFavorites([video], completion: completion)
            }
        }
    }
    
    func removeFromFavorites(_ video: VideoContent, completion: @escaping (Result<Void, AppError>) -> Void) {
        loadFavorites { [weak self] result in
            switch result {
            case .success(var favorites):
                favorites.removeAll { $0.id == video.id }
                self?.saveFavorites(favorites, completion: completion)
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func loadFavorites(completion: @escaping (Result<[VideoContent], AppError>) -> Void) {
        storageService.load(forKey: AppConstants.Storage.favoritesKey) { result in
            switch result {
            case .success(let data):
                if let data = data,
                   let favorites = try? JSONDecoder().decode([VideoContent].self, from: data) {
                    completion(.success(favorites))
                } else {
                    completion(.success([]))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    func isFavorite(_ video: VideoContent, completion: @escaping (Bool) -> Void) {
        loadFavorites { result in
            switch result {
            case .success(let favorites):
                completion(favorites.contains { $0.id == video.id })
            case .failure:
                completion(false)
            }
        }
    }
    
    private func saveFavorites(_ favorites: [VideoContent], completion: @escaping (Result<Void, AppError>) -> Void) {
        do {
            let data = try JSONEncoder().encode(favorites)
            storageService.save(data, forKey: AppConstants.Storage.favoritesKey, completion: completion)
        } catch {
            completion(.failure(AppError.data(.encodingFailed)))
        }
    }
}