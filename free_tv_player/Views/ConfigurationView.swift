//
//  ConfigurationView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI

struct ConfigurationView: View {
    @ObservedObject var viewModel: DataSourceViewModel
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingAddSourceSheet = false
    @State private var showingImportSheet = false
    @State private var showingExportSheet = false
    @State private var selectedSource: DataSource?
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                if viewModel.configuration.sources.isEmpty {
                    emptyStateView
                } else {
                    sourceListView
                }
            }
            .navigationBarHidden(true)
            .sheet(isPresented: $showingAddSourceSheet) {
                AddDataSourceView { source in
                    viewModel.addDataSource(source)
                }
            }
            .sheet(isPresented: $showingImportSheet) {
                ImportConfigurationView(viewModel: viewModel)
            }
            .sheet(isPresented: $showingExportSheet) {
                ExportConfigurationView(configuration: viewModel.configuration)
            }
            .alert("删除数据源", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    if let source = selectedSource {
                        viewModel.removeDataSource(source)
                    }
                }
            } message: {
                Text("确定要删除数据源 \"\(selectedSource?.name ?? "")\" 吗？此操作无法撤销。")
            }
            .errorAlert(error: $viewModel.error)
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            // Close Button
            Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(.accentColor)
            
            Spacer()
            
            // Title
            Text("数据源配置")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
            
            Spacer()
            
            // Menu Button
            Menu {
                Button(action: { showingAddSourceSheet = true }) {
                    Label("添加数据源", systemImage: "plus")
                }
                
                Button(action: { showingImportSheet = true }) {
                    Label("导入配置", systemImage: "square.and.arrow.down")
                }
                
                if !viewModel.configuration.sources.isEmpty {
                    Button(action: { showingExportSheet = true }) {
                        Label("导出配置", systemImage: "square.and.arrow.up")
                    }
                    
                    Divider()
                    
                    Button("清空所有", role: .destructive) {
                        viewModel.clearConfiguration()
                    }
                }
            } label: {
                Image(systemName: "ellipsis.circle")
                    .font(.title3)
                    .foregroundColor(.accent)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "server.rack")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("暂无数据源")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("添加数据源以开始使用应用")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                Button("添加数据源") {
                    showingAddSourceSheet = true
                }
                .buttonStyle(PrimaryButtonStyle())
                
                Button("导入配置") {
                    showingImportSheet = true
                }
                .buttonStyle(SecondaryButtonStyle())
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Source List View
    private var sourceListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.configuration.sources, id: \.key) { source in
                    DataSourceRowView(
                        source: source,
                        onEdit: { editSource(source) },
                        onDelete: { deleteSource(source) },
                        onToggle: { viewModel.toggleDataSourceVisibility(source) }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 40)
        }
    }
    
    // MARK: - Private Methods
    private func editSource(_ source: DataSource) {
        // TODO: Implement edit functionality
    }
    
    private func deleteSource(_ source: DataSource) {
        selectedSource = source
        showingDeleteAlert = true
    }
}

// MARK: - Data Source Row View
struct DataSourceRowView: View {
    let source: DataSource
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onToggle: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // Status Indicator
            Circle()
                .fill(source.visible ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
            
            // Source Info
            VStack(alignment: .leading, spacing: 4) {
                Text(source.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                
                Text(source.api)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    // Type Badge
                    Text(source.type.rawValue.uppercased())
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.accent.opacity(0.2))
                        )
                        .foregroundColor(.accent)
                    
                    // Searchable Badge
                    if source.searchable {
                        Text("可搜索")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.blue.opacity(0.2))
                            )
                            .foregroundColor(.blue)
                    }
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 8) {
                // Toggle Visibility
                Button(action: onToggle) {
                    Image(systemName: source.isVisible ? "eye" : "eye.slash")
                        .font(.title3)
                        .foregroundColor(source.isVisible ? .accent : .gray)
                }
                
                // Edit Button
                Button(action: onEdit) {
                    Image(systemName: "pencil")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
                
                // Delete Button
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .font(.title3)
                        .foregroundColor(.red)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBackground)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray, lineWidth: 0.5)
        )
    }
}

// MARK: - Add Data Source View
struct AddDataSourceView: View {
    let onAdd: (DataSource) -> Void
    @Environment(\.presentationMode) var presentationMode
    
    @State private var name = ""
    @State private var api = ""
    @State private var selectedType = DataSourceType.json
    @State private var searchable = true
    @State private var playerUrl = ""
    @State private var ext = ""
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                VStack(alignment: .leading, spacing: 8) {
                Text("基本信息")
                    .font(.headline)
                    .padding(.bottom, 4)
                    TextField("数据源名称", text: $name)
                    TextField("API 地址", text: $api)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                }
                
                Section("配置选项") {
                    Picker("数据源类型", selection: $selectedType) {
                        ForEach(DataSourceType.allCases, id: \.self) { type in
                            Text(type.rawValue.uppercased()).tag(type)
                        }
                    }
                    
                    Toggle("支持搜索", isOn: $searchable)
                }
                
                Section("高级选项") {
                    TextField("播放器地址 (可选)", text: $playerUrl)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                    
                    TextField("扩展参数 (可选)", text: $ext)
                        .autocapitalization(.none)
                }
            }
            .navigationTitle("添加数据源")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        addDataSource()
                    }
                    .disabled(!isFormValid)
                }
            }
            .alert("错误", isPresented: $showingError) {
                Button("确定") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !name.trimmed.isEmpty && !api.trimmed.isEmpty && api.isValidURL
    }
    
    // MARK: - Private Methods
    private func addDataSource() {
        guard isFormValid else {
            errorMessage = "请填写完整的数据源信息"
            showingError = true
            return
        }
        
        let source = DataSource(
            key: UUID().uuidString,
            name: name.trimmed,
            api: api.trimmed,
            type: selectedType,
            searchable: searchable,
            filterable: true,
            visible: true,
            playerUrl: playerUrl.trimmed.isEmpty ? nil : playerUrl.trimmed,
            ext: ext.trimmed.isEmpty ? nil : [String: String]()
        )
        
        onAdd(source)
        presentationMode.wrappedValue.dismiss()
    }
}

// MARK: - Import Configuration View
struct ImportConfigurationView: View {
    @ObservedObject var viewModel: DataSourceViewModel
    @Environment(\.presentationMode) var presentationMode
    
    @State private var urlText = ""
    @State private var showingFilePicker = false
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // URL Import
                VStack(alignment: .leading, spacing: 12) {
                    Text("从 URL 导入")
                        .font(.headline)
                        .foregroundColor(.primaryText)
                    
                    TextField("配置文件 URL", text: $urlText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    
                    Button("导入") {
                        importFromURL()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .disabled(urlText.trimmed.isEmpty || !urlText.isValidURL)
                }
                
                // Divider
                HStack {
                    Rectangle()
                        .fill(Color.border)
                        .frame(height: 1)
                    
                    Text("或")
                        .font(.subheadline)
                        .foregroundColor(.secondaryText)
                        .padding(.horizontal, 16)
                    
                    Rectangle()
                        .fill(Color.border)
                        .frame(height: 1)
                }
                
                // File Import
                VStack(alignment: .leading, spacing: 12) {
                    Text("从文件导入")
                        .font(.headline)
                        .foregroundColor(.primaryText)
                    
                    Button("选择文件") {
                        showingFilePicker = true
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
                
                Spacer()
            }
            .padding(20)
            .navigationTitle("导入配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .overlay(
                Group {
                    if isLoading {
                        LoadingView()
                    }
                }
            )
        }
        .fileImporter(
            isPresented: $showingFilePicker,
            allowedContentTypes: [.json],
            allowsMultipleSelection: false
        ) { result in
            handleFileImport(result)
        }
    }
    
    // MARK: - Private Methods
    private func importFromURL() {
        guard let url = URL(string: urlText.trimmed) else { return }
        
        isLoading = true
        
        Task {
            do {
                try await viewModel.loadConfiguration(from: url.absoluteString)
                await MainActor.run {
                    isLoading = false
                    presentationMode.wrappedValue.dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    viewModel.error = error as? AppError ?? .configuration(.invalidFormat)
                }
            }
        }
    }
    
    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            
            isLoading = true
            
            Task {
                do {
                    let data = try Data(contentsOf: url)
                    try await viewModel.loadConfiguration(from: String(data: data, encoding: .utf8) ?? "")
                    await MainActor.run {
                        isLoading = false
                        presentationMode.wrappedValue.dismiss()
                    }
                } catch {
                    await MainActor.run {
                        isLoading = false
                        viewModel.error = error as? AppError ?? .configuration(.invalidFormat)
                    }
                }
            }
            
        case .failure(let error):
            viewModel.error = .configurationError(error.localizedDescription)
        }
    }
}

// MARK: - Export Configuration View
struct ExportConfigurationView: View {
    let configuration: DataSourceConfiguration
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingShareSheet = false
    @State private var exportData: Data?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Configuration Info
                VStack(alignment: .leading, spacing: 16) {
                    Text("配置信息")
                        .font(.headline)
                        .foregroundColor(.primaryText)
                    
                    VStack(spacing: 8) {
                        InfoRow(title: "数据源数量", value: "\(configuration.sources.count)")
                        InfoRow(title: "直播频道组", value: "\(configuration.lives?.count ?? 0)")
                        InfoRow(title: "解析规则", value: "\(configuration.parseRules.count)")
                    }
                }
                
                // Export Options
                VStack(spacing: 12) {
                    Button("导出配置文件") {
                        exportConfiguration()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    
                    Button("复制配置 JSON") {
                        copyConfigurationJSON()
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
                
                Spacer()
            }
            .padding(20)
            .navigationTitle("导出配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingShareSheet) {
                if let data = exportData {
                    ShareSheet(items: [data])
                }
            }
        }
    }
    
    // MARK: - Private Methods
    private func exportConfiguration() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            exportData = try encoder.encode(configuration)
            showingShareSheet = true
        } catch {
            // Handle error
        }
    }
    
    private func copyConfigurationJSON() {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(configuration)
            if let jsonString = String(data: data, encoding: .utf8) {
                NSPasteboard.general.setString(jsonString, forType: .string)
            }
        } catch {
            // Handle error
        }
    }
}

// MARK: - Info Row
struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondaryText)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primaryText)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview
struct ConfigurationView_Previews: PreviewProvider {
    static var previews: some View {
        ConfigurationView(viewModel: DataSourceViewModel())
            .preferredColorScheme(.dark)
    }
}