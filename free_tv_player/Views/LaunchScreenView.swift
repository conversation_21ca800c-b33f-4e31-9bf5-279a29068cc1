//
//  LaunchScreenView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI

struct LaunchScreenView: View {
    @State private var isAnimating = false
    @State private var showMainApp = false
    @State private var logoScale: CGFloat = 0.5
    @State private var logoOpacity: Double = 0.0
    @State private var textOpacity: Double = 0.0
    
    var body: some View {
        ZStack {
            // Background
            LinearGradient(
                colors: [
                    Color.accentColor.opacity(0.8),
                    Color.accentColor.opacity(0.6),
                    Color.primaryBackground
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 24) {
                Spacer()
                
                // App Logo
                ZStack {
                    // Background circle
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.white.opacity(0.2), Color.white.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .scaleEffect(logoScale)
                        .opacity(logoOpacity)
                    
                    // TV Icon
                    Image(systemName: "tv")
                        .font(.system(size: 60, weight: .light))
                        .foregroundColor(.white)
                        .scaleEffect(logoScale)
                        .opacity(logoOpacity)
                }
                
                // App Name
                VStack(spacing: 8) {
                    Text(AppConstants.App.name)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .opacity(textOpacity)
                    
                    Text("免费电视播放器")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                        .opacity(textOpacity)
                }
                
                Spacer()
                
                // Loading Indicator
                VStack(spacing: 16) {
                    // Custom loading animation
                    HStack(spacing: 8) {
                        ForEach(0..<3) { index in
                            Circle()
                                .fill(Color.white)
                                .frame(width: 8, height: 8)
                                .scaleEffect(isAnimating ? 1.0 : 0.5)
                                .animation(
                                    Animation.easeInOut(duration: 0.6)
                                        .repeatForever()
                                        .delay(Double(index) * 0.2),
                                    value: isAnimating
                                )
                        }
                    }
                    .opacity(textOpacity)
                    
                    Text("正在启动...")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .opacity(textOpacity)
                }
                .padding(.bottom, 50)
            }
        }
        .onAppear {
            startAnimations()
        }
        .sheet(isPresented: $showMainApp) {
            HomeView()
        }
    }
    
    private func startAnimations() {
        // Logo animation
        withAnimation(.easeOut(duration: 0.8)) {
            logoScale = 1.0
            logoOpacity = 1.0
        }
        
        // Text animation
        withAnimation(.easeOut(duration: 0.8).delay(0.3)) {
            textOpacity = 1.0
        }
        
        // Loading animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isAnimating = true
        }
        
        // Navigate to main app
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            withAnimation(.easeInOut(duration: 0.5)) {
                showMainApp = true
            }
        }
    }
}

// MARK: - Onboarding View
struct OnboardingView: View {
    @State private var currentPage = 0
    @State private var showMainApp = false
    
    let pages = [
        OnboardingPage(
            title: "欢迎使用 FreeTVPlayer",
            subtitle: "免费观看各种电视节目和电影",
            systemImage: "tv",
            description: "支持多种视频源，提供高质量的观看体验"
        ),
        OnboardingPage(
            title: "多源支持",
            subtitle: "添加您喜欢的视频源",
            systemImage: "plus.circle",
            description: "支持多种格式的视频源配置，轻松添加和管理"
        ),
        OnboardingPage(
            title: "离线观看",
            subtitle: "缓存视频离线播放",
            systemImage: "arrow.down.circle",
            description: "支持视频缓存功能，随时随地观看您喜爱的内容"
        ),
        OnboardingPage(
            title: "个性化体验",
            subtitle: "定制您的观看偏好",
            systemImage: "heart",
            description: "收藏喜爱的视频，记录观看历史，打造专属体验"
        )
    ]
    
    var body: some View {
        ZStack {
            Color.primaryBackground
                .ignoresSafeArea()
            
            VStack {
                // Page Indicator
                HStack {
                    Spacer()
                    
                    HStack(spacing: 8) {
                        ForEach(0..<pages.count, id: \.self) { index in
                            Circle()
                                .fill(index == currentPage ? Color.accentColor : Color.secondaryText)
                                .frame(width: 8, height: 8)
                                .animation(.easeInOut, value: currentPage)
                        }
                    }
                    .padding(.trailing, 20)
                    .padding(.top, 20)
                }
                
                // Content
                TabView(selection: $currentPage) {
                    ForEach(0..<pages.count, id: \.self) { index in
                        OnboardingPageView(page: pages[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(DefaultTabViewStyle())
                
                // Navigation Buttons
                HStack {
                    if currentPage > 0 {
                        Button("上一页") {
                            withAnimation {
                                currentPage -= 1
                            }
                        }
                        .foregroundColor(.secondaryText)
                    }
                    
                    Spacer()
                    
                    if currentPage < pages.count - 1 {
                        Button("下一页") {
                            withAnimation {
                                currentPage += 1
                            }
                        }
                        .buttonStyle(PrimaryButtonStyle())
                    } else {
                        Button("开始使用") {
                            showMainApp = true
                        }
                        .buttonStyle(PrimaryButtonStyle())
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 50)
            }
        }
        .sheet(isPresented: $showMainApp) {
            HomeView()
        }
    }
}

// MARK: - Onboarding Page
struct OnboardingPage {
    let title: String
    let subtitle: String
    let systemImage: String
    let description: String
}

struct OnboardingPageView: View {
    let page: OnboardingPage
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Icon
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.accentColor.opacity(0.2), Color.accentColor.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: page.systemImage)
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.accentColor)
            }
            
            // Content
            VStack(spacing: 16) {
                Text(page.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)
                    .multilineTextAlignment(.center)
                
                Text(page.subtitle)
                    .font(.title3)
                    .foregroundColor(.accentColor)
                    .multilineTextAlignment(.center)
                
                Text(page.description)
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - App State Manager
class AppStateManager: ObservableObject {
    @Published var isFirstLaunch: Bool
    @Published var showLaunchScreen: Bool = true
    
    init() {
        self.isFirstLaunch = !UserDefaults.standard.bool(forKey: "hasLaunchedBefore")
    }
    
    func completeLaunch() {
        showLaunchScreen = false
        if isFirstLaunch {
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
            isFirstLaunch = false
        }
    }
}

// MARK: - Main App Entry Point
struct AppEntryView: View {
    @StateObject private var appState = AppStateManager()
    
    var body: some View {
        Group {
            if appState.showLaunchScreen {
                if appState.isFirstLaunch {
                    OnboardingView()
                } else {
                    LaunchScreenView()
                }
            } else {
                HomeView()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSApplication.didFinishLaunchingNotification)) { _ in
            // Handle app launch completion
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                appState.completeLaunch()
            }
        }
    }
}

// MARK: - Preview
struct LaunchScreenView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            LaunchScreenView()
                .preferredColorScheme(.dark)
            
            OnboardingView()
                .preferredColorScheme(.dark)
        }
    }
}