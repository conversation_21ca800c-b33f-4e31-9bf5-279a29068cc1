//
//  VideoPlayerView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI
import AVFoundation
import AVKit

struct VideoPlayerView: View {
    let url: URL
    let title: String
    let onDismiss: () -> Void
    
    @StateObject private var playerManager = PlayerManager.shared
    @State private var showControls = true
    @State private var controlsTimer: Timer?
    @State private var isFullscreen = false
    @State private var showSpeedMenu = false
    @State private var showQualityMenu = false
    @State private var dragOffset: CGSize = .zero
    @State private var brightness: Double = 0.5
    @State private var volume: Float = 0.5
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                Color.black
                    .ignoresSafeArea()
                
                // Video Player
                if let player = playerManager.player {
                    VideoPlayer(player: player)
                        .onTapGesture {
                            toggleControls()
                        }
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    handleDragGesture(value, in: geometry)
                                }
                                .onEnded { _ in
                                    dragOffset = .zero
                                }
                        )
                } else {
                    LoadingView(message: "正在加载视频...")
                }
                
                // Custom Controls Overlay
                if showControls {
                    controlsOverlay(geometry: geometry)
                        .transition(.opacity)
                }
                
                // Gesture Feedback
                if dragOffset != .zero {
                    gestureIndicator(geometry: geometry)
                }
                
                // Error View
                if let error = playerManager.error {
                    ErrorView(error: error) {
                        playerManager.play(url: url)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .statusBarHidden(isFullscreen)
        .onAppear {
            setupPlayer()
        }
        .onDisappear {
            cleanup()
        }
        .sheet(isPresented: $showSpeedMenu) {
            SpeedSelectionView(currentSpeed: playerManager.playbackRate) { speed in
                playerManager.setPlaybackSpeed(speed)
            }
        }
        .sheet(isPresented: $showQualityMenu) {
            QualitySelectionView()
        }
    }
    
    // MARK: - Controls Overlay
    private func controlsOverlay(geometry: GeometryProxy) -> some View {
        VStack {
            // Top Controls
            topControls
            
            Spacer()
            
            // Center Controls
            centerControls
            
            Spacer()
            
            // Bottom Controls
            bottomControls(geometry: geometry)
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.black.opacity(0.7), Color.clear, Color.black.opacity(0.7)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    private var topControls: some View {
        HStack {
            Button(action: onDismiss) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
                .lineLimit(1)
            
            Spacer()
            
            Button(action: { showQualityMenu = true }) {
                Image(systemName: "gear")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
    }
    
    private var centerControls: some View {
        HStack(spacing: 40) {
            Button(action: { playerManager.seekBackward(15) }) {
                Image(systemName: "gobackward.15")
                    .font(.title)
                    .foregroundColor(.white)
            }
            
            Button(action: playerManager.togglePlayPause) {
                Image(systemName: playerManager.isPlaying ? "pause.fill" : "play.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.white)
            }
            
            Button(action: { playerManager.seekForward(15) }) {
                Image(systemName: "goforward.15")
                    .font(.title)
                    .foregroundColor(.white)
            }
        }
    }
    
    private func bottomControls(geometry: GeometryProxy) -> some View {
        VStack(spacing: 12) {
            // Progress Bar
            progressBar(geometry: geometry)
            
            // Control Buttons
            HStack {
                // Time Display
                Text("\(playerManager.currentTime.formattedDuration) / \(playerManager.duration.formattedDuration)")
                    .font(.caption)
                    .foregroundColor(.white)
                    .monospacedDigit()
                
                Spacer()
                
                // Speed Button
                Button(action: { showSpeedMenu = true }) {
                    Text("\(playerManager.playbackRate, specifier: "%.1f")x")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.white.opacity(0.2))
                        .cornerRadius(4)
                }
                
                // Fullscreen Button
                Button(action: toggleFullscreen) {
                    Image(systemName: isFullscreen ? "arrow.down.right.and.arrow.up.left" : "arrow.up.left.and.arrow.down.right")
                        .font(.caption)
                        .foregroundColor(.white)
                }
            }
        }
    }
    
    private func progressBar(geometry: GeometryProxy) -> some View {
        GeometryReader { progressGeometry in
            ZStack(alignment: .leading) {
                // Background
                Rectangle()
                    .fill(Color.white.opacity(0.3))
                    .frame(height: 4)
                
                // Progress
                Rectangle()
                    .fill(Color.accent)
                    .frame(width: progressGeometry.size.width * CGFloat(playerManager.progress), height: 4)
                
                // Buffering indicator
                if playerManager.isBuffering {
                    Rectangle()
                        .fill(Color.white.opacity(0.5))
                        .frame(width: progressGeometry.size.width * CGFloat(playerManager.progress + 0.1), height: 4)
                }
            }
            .cornerRadius(2)
            .gesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { value in
                        let progress = value.location.x / progressGeometry.size.width
                        let clampedProgress = max(0, min(1, progress))
                        let seekTime = clampedProgress * playerManager.duration
                        playerManager.seek(to: seekTime)
                    }
            )
        }
        .frame(height: 20)
    }
    
    // MARK: - Gesture Indicator
    private func gestureIndicator(geometry: GeometryProxy) -> some View {
        VStack {
            if abs(dragOffset.x) > abs(dragOffset.y) {
                // Horizontal gesture - seeking
                HStack {
                    Image(systemName: dragOffset.x > 0 ? "goforward.15" : "gobackward.15")
                    Text(dragOffset.x > 0 ? "+15s" : "-15s")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(Color.black.opacity(0.7))
                .cornerRadius(8)
            } else {
                // Vertical gesture - volume/brightness
                let isLeftSide = dragOffset.x < geometry.size.width / 2
                VStack {
                    Image(systemName: isLeftSide ? "brightness.high" : "speaker.wave.3")
                    Text(isLeftSide ? "亮度" : "音量")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(Color.black.opacity(0.7))
                .cornerRadius(8)
            }
        }
    }
    
    // MARK: - Private Methods
    private func setupPlayer() {
        playerManager.play(url: url)
        playerManager.updateNowPlayingInfo(title: title)
        startControlsTimer()
    }
    
    private func cleanup() {
        controlsTimer?.invalidate()
        playerManager.pause()
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        }
    }
    
    private func startControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showControls = false
            }
        }
    }
    
    private func toggleFullscreen() {
        withAnimation {
            isFullscreen.toggle()
        }
        
        if isFullscreen {
            // Lock to landscape
            UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
        } else {
            // Return to portrait
            UIDevice.current.setValue(UIInterfaceOrientation.portrait.rawValue, forKey: "orientation")
        }
    }
    
    private func handleDragGesture(_ value: DragGesture.Value, in geometry: GeometryProxy) {
        dragOffset = value.translation
        
        if abs(value.translation.x) > abs(value.translation.y) {
            // Horizontal gesture - seeking
            let seekAmount = value.translation.x > 0 ? 15.0 : -15.0
            if abs(value.translation.x) > 50 {
                if value.translation.x > 0 {
                    playerManager.seekForward(15)
                } else {
                    playerManager.seekBackward(15)
                }
            }
        } else {
            // Vertical gesture - volume/brightness
            let isLeftSide = value.startLocation.x < geometry.size.width / 2
            let delta = -value.translation.y / geometry.size.height
            
            if isLeftSide {
                // Brightness control (macOS not supported)
                let newBrightness = max(0, min(1, brightness + delta))
                brightness = newBrightness
            } else {
                // Volume control
                let newVolume = max(0, min(1, volume + Float(delta)))
                playerManager.volume = newVolume
            }
        }
    }
}

// MARK: - Speed Selection View
struct SpeedSelectionView: View {
    let currentSpeed: Float
    let onSpeedSelected: (Float) -> Void
    
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                ForEach(PlaybackSpeed.allCases, id: \.self) { speed in
                    Button {
                        onSpeedSelected(speed.rawValue)
                        presentationMode.wrappedValue.dismiss()
                    } label: {
                        HStack {
                            Text(speed.displayName)
                                .foregroundColor(.primaryText)
                            
                            Spacer()
                            
                            if abs(currentSpeed - speed.rawValue) < 0.01 {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.accent)
                            }
                        }
                    }
                }
            }
            .navigationTitle("播放速度")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Quality Selection View
struct QualitySelectionView: View {
    @Environment(\.presentationMode) var presentationMode
    
    let qualities = ["自动", "1080p", "720p", "480p", "360p"]
    @State private var selectedQuality = "自动"
    
    var body: some View {
        NavigationView {
            List {
                ForEach(qualities, id: \.self) { quality in
                    Button {
                        selectedQuality = quality
                        presentationMode.wrappedValue.dismiss()
                    } label: {
                        HStack {
                            Text(quality)
                                .foregroundColor(.primaryText)
                            
                            Spacer()
                            
                            if selectedQuality == quality {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.accent)
                            }
                        }
                    }
                }
            }
            .navigationTitle("视频质量")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Mini Player View
struct MiniPlayerView: View {
    @StateObject private var playerManager = PlayerManager.shared
    let title: String
    let onTap: () -> Void
    let onClose: () -> Void
    
    var body: some View {
        HStack {
            // Thumbnail
            Rectangle()
                .fill(Color.gray)
                .frame(width: 60, height: 40)
                .cornerRadius(4)
            
            // Info
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                    .foregroundColor(.primaryText)
                
                Text("\(playerManager.currentTime.formattedDuration) / \(playerManager.duration.formattedDuration)")
                    .font(.caption2)
                    .foregroundColor(.secondaryText)
                    .monospacedDigit()
            }
            
            Spacer()
            
            // Controls
            HStack(spacing: 12) {
                Button(action: playerManager.togglePlayPause) {
                    Image(systemName: playerManager.isPlaying ? "pause.fill" : "play.fill")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
                
                Button(action: onClose) {
                    Image(systemName: "xmark")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.secondaryBackground)
        .cornerRadius(8)
        .shadow(radius: 4)
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Picture in Picture View
struct PictureInPictureView: View {
    @StateObject private var playerManager = PlayerManager.shared
    @State private var pipController: AVPictureInPictureController?
    
    var body: some View {
        VStack {
            if playerManager.supportsPictureInPicture {
                Button("开启画中画") {
                    enablePictureInPicture()
                }
                .buttonStyle(PrimaryButtonStyle())
            } else {
                Text("设备不支持画中画功能")
                    .foregroundColor(.secondaryText)
            }
        }
        .onDisappear {
            pipController?.stopPictureInPicture()
        }
    }
    
    private func enablePictureInPicture() {
        pipController = playerManager.enablePictureInPicture()
        pipController?.startPictureInPicture()
    }
}

// MARK: - Preview
struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerView(
            url: URL(string: "https://example.com/video.mp4")!,
            title: "示例视频",
            onDismiss: {}
        )
        .preferredColorScheme(.dark)
    }
}