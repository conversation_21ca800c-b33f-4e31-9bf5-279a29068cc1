//
//  VideoCardView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI

struct VideoCardView: View {
    let video: VideoContent
    @State private var imageLoadFailed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Video Thumbnail
            thumbnailView
            
            // Video Info
            VStack(alignment: .leading, spacing: 4) {
                Text(video.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                if !video.displayDescription.isEmpty {
                    Text(video.displayDescription)
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
                
                // Video Type Badge
                if !video.displayType.isEmpty {
                    Text(video.displayType)
                        .font(.caption2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.accent.opacity(0.2))
                        )
                        .foregroundColor(.accent)
                }
            }
            .padding(.horizontal, 8)
            .padding(.bottom, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBackground)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
        )
    }
    
    // MARK: - Thumbnail View
    private var thumbnailView: some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.2))
                .aspectRatio(16/9, contentMode: .fit)
            
            if imageLoadFailed || !video.hasImage {
                // Placeholder
                VStack(spacing: 8) {
                    Image(systemName: "tv")
                        .font(.title2)
                        .foregroundColor(.secondaryText)
                    
                    Text("暂无图片")
                        .font(.caption2)
                        .foregroundColor(.secondaryText)
                }
            } else {
                // Async Image
                AsyncImage(url: video.imageURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipped()
                } placeholder: {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .accent))
                        .scaleEffect(0.8)
                }
                .onFailure {
                    imageLoadFailed = true
                }
            }
            
            // Play Button Overlay
            Circle()
                .fill(Color.black.opacity(0.6))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "play.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .offset(x: 2) // Slight offset for visual balance
                )
        }
        .cornerRadius(8)
        .padding(8)
    }
}

// MARK: - AsyncImage Extension
struct AsyncImageView<Content: View, Placeholder: View>: View {
    let url: URL?
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    let onFailure: () -> Void
    
    @State private var phase: AsyncImagePhase = .empty
    
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder,
        onFailure: @escaping () -> Void = {}
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
        self.onFailure = onFailure
    }
    
    var body: some View {
        AsyncImage(url: url) { phase in
            switch phase {
            case .empty:
                placeholder()
            case .success(let image):
                content(image)
            case .failure(_):
                placeholder()
                    .onAppear {
                        onFailure()
                    }
            @unknown default:
                placeholder()
            }
        }
    }
}

// MARK: - AsyncImage Convenience Extension
extension AsyncImage {
    func onFailure(_ action: @escaping () -> Void) -> some View {
        self.overlay(
            Color.clear
                .onReceive(NotificationCenter.default.publisher(for: .imageLoadFailed)) { _ in
                    action()
                }
        )
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let imageLoadFailed = Notification.Name("imageLoadFailed")
}

// MARK: - Large Video Card View
struct LargeVideoCardView: View {
    let video: VideoContent
    @State private var imageLoadFailed = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Thumbnail
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: 120, height: 68)
                
                if imageLoadFailed || !video.hasImage {
                    Image(systemName: "tv")
                        .font(.title3)
                        .foregroundColor(.secondaryText)
                } else {
                    AsyncImage(url: video.imageURL) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 68)
                            .clipped()
                    } placeholder: {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .accent))
                            .scaleEffect(0.6)
                    }
                    .onFailure {
                        imageLoadFailed = true
                    }
                }
                
                // Play Button
                Circle()
                    .fill(Color.black.opacity(0.6))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "play.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.white)
                            .offset(x: 1)
                    )
            }
            .cornerRadius(8)
            
            // Video Info
            VStack(alignment: .leading, spacing: 6) {
                Text(video.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                if !video.displayDescription.isEmpty {
                    Text(video.displayDescription)
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Type Badge
                if !video.displayType.isEmpty {
                    HStack {
                        Text(video.displayType)
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.accent.opacity(0.2))
                            )
                            .foregroundColor(.accent)
                        
                        Spacer()
                    }
                }
            }
            
            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBackground)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.border, lineWidth: 0.5)
        )
    }
}

// MARK: - Preview
struct VideoCardView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleVideo = VideoContent(
            id: "1",
            name: "示例视频标题",
            type: "电影",
            pic: "https://example.com/image.jpg",
            lang: "国语",
            area: "中国大陆",
            year: "2023",
            note: "HD",
            actor: "演员A,演员B",
            director: "导演A",
            des: "这是一个示例视频的描述信息，用于展示视频卡片的布局效果。",
            last: "2023-01-01",
            dt: "2023-01-01",
            tid: 1,
            typeName: "动作片",
            state: 1,
            remarks: "完结",
            serial: 1,
            doubanScore: "8.5",
            vodPlayFrom: "播放源1",
            vodPlayUrl: "第1集$https://example.com/ep1.m3u8",
            vodDownFrom: nil,
            vodDownUrl: nil
        )
        
        VStack(spacing: 20) {
            VideoCardView(video: sampleVideo)
                .frame(width: 180)
            
            LargeVideoCardView(video: sampleVideo)
        }
        .padding()
        .background(Color.primaryBackground)
        .preferredColorScheme(.dark)
    }
}