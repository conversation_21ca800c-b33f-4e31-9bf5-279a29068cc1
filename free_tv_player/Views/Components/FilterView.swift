//
//  FilterView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI

struct FilterView: View {
    let currentFilter: VideoFilter
    let onApply: (VideoFilter) -> Void
    
    @Environment(\.presentationMode) var presentationMode
    @State private var selectedType: String?
    @State private var selectedYear: String?
    @State private var selectedRegion: String?
    @State private var selectedSort: String?
    
    let types = ["电影", "电视剧", "综艺", "动漫", "纪录片", "短剧"]
    let years = Array(2010...2024).reversed().map { String($0) }
    let regions = ["大陆", "香港", "台湾", "美国", "日本", "韩国", "泰国", "英国", "法国"]
    let sortOptions = ["最新", "最热", "评分", "年份"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Type Filter
                    filterSection(
                        title: "类型",
                        options: types,
                        selection: $selectedType
                    )
                    
                    // Year Filter
                    filterSection(
                        title: "年份",
                        options: years,
                        selection: $selectedYear
                    )
                    
                    // Region Filter
                    filterSection(
                        title: "地区",
                        options: regions,
                        selection: $selectedRegion
                    )
                    
                    // Sort Filter
                    filterSection(
                        title: "排序",
                        options: sortOptions,
                        selection: $selectedSort
                    )
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        Button("应用筛选") {
                            applyFilter()
                        }
                        .buttonStyle(PrimaryButtonStyle())
                        
                        Button("重置筛选") {
                            resetFilter()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                    .padding(.top, 16)
                }
                .padding(20)
            }
            .navigationTitle("筛选")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("完成") {
                        applyFilter()
                    }
                }
            }
        }
        .onAppear {
            loadCurrentFilter()
        }
    }
    
    // MARK: - Filter Section
    private func filterSection(
        title: String,
        options: [String],
        selection: Binding<String?>
    ) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                // All option
                Button("全部") {
                    selection.wrappedValue = nil
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(selection.wrappedValue == nil ? Color.accentColor : Color.secondaryBackground)
                )
                .foregroundColor(selection.wrappedValue == nil ? .white : .primaryText)
                .font(.subheadline)
                
                ForEach(options, id: \.self) { option in
                    Button(option) {
                        selection.wrappedValue = option
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(selection.wrappedValue == option ? Color.accentColor : Color.secondaryBackground)
                    )
                    .foregroundColor(selection.wrappedValue == option ? .white : .primaryText)
                    .font(.subheadline)
                }
            }
        }
    }
    
    // MARK: - Private Methods
    private func loadCurrentFilter() {
        selectedType = currentFilter.category
        selectedYear = currentFilter.year
        selectedRegion = currentFilter.area
        selectedSort = currentFilter.sort
    }

    private func applyFilter() {
        let filter = VideoFilter(
            category: selectedType,
            area: selectedRegion,
            year: selectedYear,
            lang: nil,
            letter: nil,
            by: nil,
            sort: selectedSort
        )

        onApply(filter)
        presentationMode.wrappedValue.dismiss()
    }
    
    private func resetFilter() {
        selectedType = nil
        selectedYear = nil
        selectedRegion = nil
        selectedSort = nil
    }
}

// MARK: - Quick Filter View
struct QuickFilterView: View {
    @Binding var selectedCategory: VideoCategory?
    @Binding var selectedSort: String?
    
    let categories: [VideoCategory]
    let sortOptions = ["最新", "最热", "评分"]
    
    var body: some View {
        VStack(spacing: 12) {
            // Categories
            if !categories.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        Button("全部") {
                            selectedCategory = nil
                        }
                        .quickFilterButtonStyle(isSelected: selectedCategory == nil)
                        
                        ForEach(categories, id: \.id) { category in
                            Button(category.name) {
                                selectedCategory = category
                            }
                            .quickFilterButtonStyle(isSelected: selectedCategory?.id == category.id)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            
            // Sort Options
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(sortOptions, id: \.self) { option in
                        Button(option) {
                            selectedSort = selectedSort == option ? nil : option
                        }
                        .quickFilterButtonStyle(isSelected: selectedSort == option)
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Quick Filter Button Style
struct QuickFilterButtonStyle: ViewModifier {
    let isSelected: Bool
    
    func body(content: Content) -> some View {
        content
            .font(.subheadline)
            .padding(.horizontal, 16)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.accentColor : Color.secondaryBackground)
            )
            .foregroundColor(isSelected ? .white : .primaryText)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.gray, lineWidth: isSelected ? 0 : 0.5)
            )
    }
}

extension View {
    func quickFilterButtonStyle(isSelected: Bool) -> some View {
        self.modifier(QuickFilterButtonStyle(isSelected: isSelected))
    }
}

// MARK: - Advanced Filter View
struct AdvancedFilterView: View {
    @Binding var filter: VideoFilter
    let onApply: () -> Void
    let onReset: () -> Void
    
    @State private var showingYearPicker = false
    @State private var showingRatingPicker = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("高级筛选")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)
            
            // Type Selection
            filterRow(title: "类型", value: filter.type ?? "全部") {
                // Show type picker
            }
            
            // Year Selection
            filterRow(title: "年份", value: filter.year ?? "全部") {
                showingYearPicker = true
            }
            
            // Region Selection
            filterRow(title: "地区", value: filter.region ?? "全部") {
                // Show region picker
            }
            
            // Rating Filter
            if let rating = filter.rating {
                filterRow(title: "评分", value: "\(rating, specifier: "%.1f") 分以上") {
                    showingRatingPicker = true
                }
            } else {
                filterRow(title: "评分", value: "全部") {
                    showingRatingPicker = true
                }
            }
            
            // Duration Filter
            if let duration = filter.duration {
                filterRow(title: "时长", value: "\(duration) 分钟以上") {
                    // Show duration picker
                }
            } else {
                filterRow(title: "时长", value: "全部") {
                    // Show duration picker
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 12) {
                Button("重置") {
                    onReset()
                }
                .buttonStyle(SecondaryButtonStyle())
                
                Button("应用") {
                    onApply()
                }
                .buttonStyle(PrimaryButtonStyle())
            }
        }
        .padding(20)
        .sheet(isPresented: $showingYearPicker) {
            YearPickerView(selectedYear: Binding(
                get: { filter.year },
                set: { filter.year = $0 }
            ))
        }
        .sheet(isPresented: $showingRatingPicker) {
            RatingPickerView(selectedRating: Binding(
                get: { filter.rating },
                set: { filter.rating = $0 }
            ))
        }
    }
    
    private func filterRow(title: String, value: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.primaryText)
                
                Spacer()
                
                Text(value)
                    .font(.subheadline)
                    .foregroundColor(.accent)
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.secondaryBackground)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Year Picker View
struct YearPickerView: View {
    @Binding var selectedYear: String?
    @Environment(\.presentationMode) var presentationMode
    
    let years = Array(2000...2024).reversed().map { String($0) }
    
    var body: some View {
        NavigationView {
            List {
                Button("全部") {
                    selectedYear = nil
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(selectedYear == nil ? .accent : .primaryText)
                
                ForEach(years, id: \.self) { year in
                    Button(year) {
                        selectedYear = year
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(selectedYear == year ? .accent : .primaryText)
                }
            }
            .navigationTitle("选择年份")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Rating Picker View
struct RatingPickerView: View {
    @Binding var selectedRating: Double?
    @Environment(\.presentationMode) var presentationMode
    
    let ratings = [9.0, 8.0, 7.0, 6.0, 5.0]
    
    var body: some View {
        NavigationView {
            List {
                Button("全部") {
                    selectedRating = nil
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(selectedRating == nil ? .accent : .primaryText)
                
                ForEach(ratings, id: \.self) { rating in
                    Button("\(rating, specifier: "%.1f") 分以上") {
                        selectedRating = rating
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(selectedRating == rating ? .accent : .primaryText)
                }
            }
            .navigationTitle("选择评分")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct FilterView_Previews: PreviewProvider {
    static var previews: some View {
        FilterView(
            currentFilter: VideoFilter(),
            onApply: { _ in }
        )
        .preferredColorScheme(.dark)
    }
}