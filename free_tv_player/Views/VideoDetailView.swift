//
//  VideoDetailView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI
import AVKit

struct VideoDetailView: View {
    let video: VideoContent
    let source: DataSource
    
    @StateObject private var viewModel: VideoDetailViewModel
    @State private var showingPlayer = false
    @State private var selectedEpisode: Episode?
    @State private var showingShareSheet = false
    @Environment(\.presentationMode) var presentationMode
    
    init(video: VideoContent, source: DataSource) {
        self.video = video
        self.source = source
        self._viewModel = StateObject(wrappedValue: VideoDetailViewModel())
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                // Header Image
                headerImageView
                
                // Video Info
                videoInfoSection
                
                // Action Buttons
                actionButtonsSection
                
                // Episodes Section
                if !video.playUrls.isEmpty {
                    episodesSection
                }
                
                // Description
                if !video.displayDescription.isEmpty {
                    descriptionSection
                }
                
                // Related Videos
                relatedVideosSection
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { presentationMode.wrappedValue.dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title3)
                        .foregroundColor(.accentColor)
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingShareSheet = true }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
            }
        }
        .onAppear {
            viewModel.setVideo(video)
        }
        .fullScreenCover(isPresented: $showingPlayer) {
            if let episode = selectedEpisode {
                VideoPlayerView(
                    url: URL(string: episode.url) ?? URL(string: "https://example.com")!,
                    title: episode.name,
                    onDismiss: { showingPlayer = false }
                )
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [video.name, video.displayDescription])
        }
        .errorAlert(error: $viewModel.error)
        .overlay(
            Group {
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(1.5)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                }
            }
        )
    }
    
    // MARK: - Header Image View
    private var headerImageView: some View {
        ZStack(alignment: .bottomLeading) {
            // Background Image
            AsyncImage(url: video.imageURL) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 250)
                    .clipped()
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 250)
                    .overlay(
                        Image(systemName: "tv")
                            .font(.system(size: 48))
                            .foregroundColor(.secondaryText)
                    )
            }
            
            // Gradient Overlay
            LinearGradient(
                gradient: Gradient(colors: [Color.clear, Color.black.opacity(0.7)]),
                startPoint: .top,
                endPoint: .bottom
            )
            .frame(height: 250)
            
            // Play Button
            VStack {
                Spacer()
                
                HStack {
                    Spacer()
                    
                    Button(action: playFirstEpisode) {
                        Circle()
                            .fill(Color.accent)
                            .frame(width: 64, height: 64)
                            .overlay(
                                Image(systemName: "play.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                    .offset(x: 2)
                            )
                    }
                    .disabled(video.playUrls.isEmpty)
                    
                    Spacer()
                }
                
                Spacer()
            }
        }
    }
    
    // MARK: - Video Info Section
    private var videoInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Title
            Text(video.name)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)
                .lineLimit(nil)
            
            // Type and Info
            HStack {
                if !video.displayType.isEmpty {
                    Text(video.displayType)
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.accent.opacity(0.2))
                        )
                        .foregroundColor(.accent)
                }
                
                Spacer()
                
                // Episode Count
                if !video.playUrls.isEmpty {
                    let totalEpisodes = video.playUrls.reduce(0) { $0 + $1.episodes.count }
                    Text("共 \(totalEpisodes) 集")
                        .font(.subheadline)
                        .foregroundColor(.secondaryText)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        HStack(spacing: 16) {
            // Play Button
            Button(action: playFirstEpisode) {
                HStack {
                    Image(systemName: "play.fill")
                    Text("播放")
                }
                .frame(maxWidth: .infinity)
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(video.playUrls.isEmpty)
            
            // Favorite Button
            Button(action: { viewModel.toggleFavorite() }) {
                Image(systemName: viewModel.isFavorite ? "heart.fill" : "heart")
                    .font(.title3)
                    .foregroundColor(viewModel.isFavorite ? .red : .accent)
            }
            .frame(width: 44, height: 44)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.secondaryBackground)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
    }
    
    // MARK: - Episodes Section
    private var episodesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("选集")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
                .padding(.horizontal, 20)
            
            ForEach(video.playUrls, id: \.name) { playUrl in
                VStack(alignment: .leading, spacing: 12) {
                    // Source Name
                    if video.playUrls.count > 1 {
                        Text(playUrl.name)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.accent)
                            .padding(.horizontal, 20)
                    }
                    
                    // Episodes Grid
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 4), spacing: 8) {
                        ForEach(playUrl.episodes, id: \.name) { episode in
                            Button(episode.name) {
                                selectedEpisode = episode
                                showingPlayer = true
                                viewModel.selectEpisode(episode)
                            }
                            .font(.subheadline)
                            .foregroundColor(.primaryText)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.secondaryBackground)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
        .padding(.top, 24)
    }
    
    // MARK: - Description Section
    private var descriptionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("简介")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
            
            Text(video.displayDescription)
                .font(.body)
                .foregroundColor(.secondaryText)
                .lineLimit(nil)
        }
        .padding(.horizontal, 20)
        .padding(.top, 24)
    }
    
    // MARK: - Related Videos Section
    private var relatedVideosSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("相关推荐")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
                .padding(.horizontal, 20)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // TODO: Implement related videos functionality
                    ForEach([VideoContent](), id: \.id) { relatedVideo in
                        NavigationLink(destination: VideoDetailView(video: relatedVideo, source: source)) {
                            VideoCardView(video: relatedVideo)
                                .frame(width: 140)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.top, 24)
        .padding(.bottom, 40)
    }
    
    // MARK: - Private Methods
    private func playFirstEpisode() {
        guard let firstPlayUrl = video.playUrls.first,
              let firstEpisode = firstPlayUrl.episodes.first else { return }
        
        selectedEpisode = firstEpisode
        showingPlayer = true
        viewModel.selectEpisode(firstEpisode)
    }
}

// MARK: - Share Sheet (macOS compatible)
struct ShareSheet: View {
    let items: [Any]
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack {
            Text("分享功能")
                .font(.title2)
                .padding()
            
            Text("macOS 版本暂不支持分享功能")
                .foregroundColor(.secondaryText)
                .padding()
            
            Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            }
            .buttonStyle(PrimaryButtonStyle())
            .padding()
        }
        .frame(width: 300, height: 200)
    }
}



// MARK: - Preview
struct VideoDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleVideo = VideoContent(
            id: "1",
            name: "示例电影标题",
            type: "电影",
            pic: "https://example.com/image.jpg",
            lang: "国语",
            area: "中国大陆",
            year: "2023",
            note: "HD",
            actor: "演员A,演员B",
            director: "导演A",
            des: "这是一个示例电影的详细描述信息，包含了电影的剧情简介、演员阵容等相关信息。",
            last: "2023-01-01",
            dt: "2023-01-01",
            tid: 1,
            typeName: "动作片",
            state: 1,
            remarks: "完结",
            serial: 1,
            doubanScore: "8.5",
            vodPlayFrom: "播放源1",
            vodPlayUrl: "第1集$https://example.com/ep1.m3u8#第2集$https://example.com/ep2.m3u8",
            vodDownFrom: nil,
            vodDownUrl: nil
        )
        
        let sampleSource = DataSource(
            key: "sample",
            name: "示例源",
            api: "https://example.com/api",
            type: .json
        )
        
        NavigationView {
            VideoDetailView(video: sampleVideo, source: sampleSource)
        }
        .preferredColorScheme(.dark)
    }
}