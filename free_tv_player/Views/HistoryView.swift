//
//  HistoryView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI

struct HistoryView: View {
    @StateObject private var viewModel = HistoryViewModel()
    @State private var showingClearAlert = false
    @State private var selectedVideo: VideoContent?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                if viewModel.isLoading {
                    loadingView
                } else if viewModel.historyItems.isEmpty {
                    emptyStateView
                } else {
                    historyListView
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                viewModel.loadHistory()
            }
            .alert("清空历史记录", isPresented: $showingClearAlert) {
                Button("取消", role: .cancel) { }
                Button("清空", role: .destructive) {
                    viewModel.clearHistory()
                }
            } message: {
                Text("确定要清空所有观看历史吗？此操作无法撤销。")
            }
            .errorAlert(error: $viewModel.error)
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Text("观看历史")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)
            
            Spacer()
            
            if !viewModel.historyItems.isEmpty {
                Button("清空") {
                    showingClearAlert = true
                }
                .font(.subheadline)
                .foregroundColor(.red)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .accent))
                .scaleEffect(1.2)
            
            Text("加载中...")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "clock")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("暂无观看历史")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("开始观看视频后，历史记录会显示在这里")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - History List View
    private var historyListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.historyItems, id: \.video.id) { item in
                    HistoryItemView(
                        item: item,
                        onTap: {
                            // Navigate to video detail
                            selectedVideo = item.video
                        },
                        onRemove: {
                            viewModel.removeFromHistory(item.video)
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 40)
        }
        .refreshable {
            viewModel.loadHistory()
        }
    }
}

// MARK: - History Item View
struct HistoryItemView: View {
    let item: HistoryItem
    let onTap: () -> Void
    let onRemove: () -> Void
    
    @State private var showingRemoveAlert = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Video Thumbnail
            AsyncImage(url: URL(string: item.video.image)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 100, height: 56)
                    .clipped()
            } placeholder: {
                Rectangle()
                    .fill(Color.tertiaryBackground)
                    .frame(width: 100, height: 56)
                    .overlay(
                        Image(systemName: "tv")
                            .font(.title3)
                            .foregroundColor(.secondaryText)
                    )
            }
            .cornerRadius(8)
            
            // Video Info
            VStack(alignment: .leading, spacing: 6) {
                Text(item.video.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                if !item.video.type.isEmpty {
                    Text(item.video.type)
                        .font(.caption)
                        .foregroundColor(.accent)
                }
                
                Spacer()
                
                Text(item.watchedAt.timeAgoDisplay)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }
            
            Spacer()
            
            // Remove Button
            Button(action: { showingRemoveAlert = true }) {
                Image(systemName: "xmark")
                    .font(.subheadline)
                    .foregroundColor(.secondaryText)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBackground)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.border, lineWidth: 0.5)
        )
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
        .alert("删除历史记录", isPresented: $showingRemoveAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                onRemove()
            }
        } message: {
            Text("确定要删除这条观看记录吗？")
        }
    }
}

// MARK: - Favorites View
struct FavoritesView: View {
    @StateObject private var viewModel = FavoritesViewModel()
    @State private var showingClearAlert = false
    @State private var selectedVideo: VideoContent?
    @State private var showingGridView = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                if viewModel.isLoading {
                    loadingView
                } else if viewModel.favoriteVideos.isEmpty {
                    emptyStateView
                } else {
                    favoritesContentView
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                viewModel.loadFavorites()
            }
            .alert("清空收藏", isPresented: $showingClearAlert) {
                Button("取消", role: .cancel) { }
                Button("清空", role: .destructive) {
                    viewModel.clearFavorites()
                }
            } message: {
                Text("确定要清空所有收藏吗？此操作无法撤销。")
            }
            .errorAlert(error: $viewModel.error)
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Text("我的收藏")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)
            
            Spacer()
            
            if !viewModel.favoriteVideos.isEmpty {
                HStack(spacing: 16) {
                    // View Toggle
                    Button(action: { showingGridView.toggle() }) {
                        Image(systemName: showingGridView ? "list.bullet" : "square.grid.2x2")
                            .font(.title3)
                            .foregroundColor(.accent)
                    }
                    
                    // Clear Button
                    Button("清空") {
                        showingClearAlert = true
                    }
                    .font(.subheadline)
                    .foregroundColor(.red)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .accent))
                .scaleEffect(1.2)
            
            Text("加载中...")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "heart")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("暂无收藏")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("收藏喜欢的视频，方便下次观看")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Favorites Content View
    private var favoritesContentView: some View {
        ScrollView {
            if showingGridView {
                gridView
            } else {
                listView
            }
        }
        .refreshable {
            viewModel.loadFavorites()
        }
    }
    
    // MARK: - Grid View
    private var gridView: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16) {
            ForEach(viewModel.favoriteVideos, id: \.id) { video in
                NavigationLink(destination: VideoDetailView(video: video, source: DataSource(key: "", name: "", api: "", type: .json))) {
                    VideoCardView(video: video)
                }
                .buttonStyle(PlainButtonStyle())
                .contextMenu {
                    Button("取消收藏", role: .destructive) {
                        viewModel.removeFromFavorites(video)
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 40)
    }
    
    // MARK: - List View
    private var listView: some View {
        LazyVStack(spacing: 12) {
            ForEach(viewModel.favoriteVideos, id: \.id) { video in
                NavigationLink(destination: VideoDetailView(video: video, source: DataSource(key: "", name: "", api: "", type: .json))) {
                    FavoriteItemView(
                        video: video,
                        onRemove: {
                            viewModel.removeFromFavorites(video)
                        }
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 40)
    }
}

// MARK: - Favorite Item View
struct FavoriteItemView: View {
    let video: VideoContent
    let onRemove: () -> Void
    
    @State private var showingRemoveAlert = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Video Thumbnail
            AsyncImage(url: URL(string: video.image)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 100, height: 56)
                    .clipped()
            } placeholder: {
                Rectangle()
                    .fill(Color.tertiaryBackground)
                    .frame(width: 100, height: 56)
                    .overlay(
                        Image(systemName: "tv")
                            .font(.title3)
                            .foregroundColor(.secondaryText)
                    )
            }
            .cornerRadius(8)
            
            // Video Info
            VStack(alignment: .leading, spacing: 6) {
                Text(video.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                if !video.type.isEmpty {
                    Text(video.type)
                        .font(.caption)
                        .foregroundColor(.accent)
                }
                
                if !video.description.isEmpty {
                    Text(video.description)
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
            }
            
            Spacer()
            
            // Favorite Button
            Button(action: { showingRemoveAlert = true }) {
                Image(systemName: "heart.fill")
                    .font(.title3)
                    .foregroundColor(.red)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondaryBackground)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.border, lineWidth: 0.5)
        )
        .alert("取消收藏", isPresented: $showingRemoveAlert) {
            Button("取消", role: .cancel) { }
            Button("确定", role: .destructive) {
                onRemove()
            }
        } message: {
            Text("确定要取消收藏这个视频吗？")
        }
    }
}

// MARK: - Live Channel View
struct LiveChannelView: View {
    let configuration: DataSourceConfiguration
    @State private var selectedGroup: LiveChannelGroup?
    @State private var showingPlayer = false
    @State private var selectedChannel: LiveChannel?
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
            
            if configuration.liveChannels.isEmpty {
                emptyStateView
            } else {
                HStack(spacing: 0) {
                    // Channel Groups
                    channelGroupsView
                    
                    // Channel List
                    if let selectedGroup = selectedGroup {
                        channelListView(for: selectedGroup)
                    }
                }
            }
        }
        .onAppear {
            selectedGroup = configuration.liveChannels.first
        }
        .fullScreenCover(isPresented: $showingPlayer) {
            if let channel = selectedChannel {
                LivePlayerView(channel: channel)
            }
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Text("电视直播")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primaryText)
            
            Spacer()
            
            Text("\(configuration.liveChannels.reduce(0) { $0 + $1.channels.count }) 个频道")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "tv")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("暂无直播频道")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("请在数据源配置中添加直播频道")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Channel Groups View
    private var channelGroupsView: some View {
        VStack(spacing: 0) {
            ForEach(configuration.liveChannels, id: \.name) { group in
                Button(action: { selectedGroup = group }) {
                    HStack {
                        Text(group.name)
                            .font(.subheadline)
                            .foregroundColor(selectedGroup?.name == group.name ? .white : .primaryText)
                        
                        Spacer()
                        
                        Text("\(group.channels.count)")
                            .font(.caption)
                            .foregroundColor(selectedGroup?.name == group.name ? .white.opacity(0.8) : .secondaryText)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        Rectangle()
                            .fill(selectedGroup?.name == group.name ? Color.accent : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Spacer()
        }
        .frame(width: 120)
        .background(Color.secondaryBackground)
    }
    
    // MARK: - Channel List View
    private func channelListView(for group: LiveChannelGroup) -> some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(group.channels, id: \.name) { channel in
                    Button(action: {
                        selectedChannel = channel
                        showingPlayer = true
                    }) {
                        HStack {
                            // Channel Logo
                            AsyncImage(url: URL(string: channel.logo ?? "")) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 32, height: 32)
                            } placeholder: {
                                Circle()
                                    .fill(Color.tertiaryBackground)
                                    .frame(width: 32, height: 32)
                                    .overlay(
                                        Text(String(channel.name.prefix(1)))
                                            .font(.caption)
                                            .fontWeight(.medium)
                                            .foregroundColor(.secondaryText)
                                    )
                            }
                            
                            Text(channel.name)
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            Spacer()
                            
                            Image(systemName: "play.circle")
                                .font(.title3)
                                .foregroundColor(.accent)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.clear)
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.vertical, 8)
        }
    }
}

// MARK: - Live Player View
struct LivePlayerView: View {
    let channel: LiveChannel
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            VStack {
                HStack {
                    Button(action: { presentationMode.wrappedValue.dismiss() }) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding()
                    }
                    
                    Text(channel.name)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [Color.black.opacity(0.6), Color.clear]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                
                Spacer()
                
                Text("直播播放器")
                    .font(.title)
                    .foregroundColor(.white)
                
                Text("URL: \(channel.url)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.top, 8)
                
                Spacer()
            }
        }
    }
}

// MARK: - Preview
struct HistoryView_Previews: PreviewProvider {
    static var previews: some View {
        HistoryView()
            .preferredColorScheme(.dark)
    }
}

struct FavoritesView_Previews: PreviewProvider {
    static var previews: some View {
        FavoritesView()
            .preferredColorScheme(.dark)
    }
}