//
//  SettingsView.swift
//  FreeTVPlayer
//
//  Created by Developer on 2025/1/27.
//

import SwiftUI
import AVFoundation

struct SettingsView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @StateObject private var storageService = StorageService.shared
    
    @State private var showingClearCacheAlert = false
    @State private var showingClearHistoryAlert = false
    @State private var showingAbout = false
    @State private var cacheSize: String = "计算中..."
    
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                // Player Settings
                Section("播放设置") {
                    playerTypeRow
                    autoplayRow
                    backgroundPlayRow
                    hardwareDecodingRow
                }
                
                // Display Settings
                Section("显示设置") {
                    themeRow
                    gridColumnsRow
                    showThumbnailsRow
                    landscapeOrientationRow
                }
                
                // Network Settings
                Section("网络设置") {
                    timeoutRow
                    maxConcurrentRow
                    useWiFiOnlyRow
                }
                
                // Storage Settings
                Section("存储设置") {
                    cacheSizeRow
                    clearCacheRow
                    clearHistoryRow
                    maxCacheSizeRow
                }
                
                // Privacy Settings
                Section("隐私设置") {
                    saveHistoryRow
                    saveFavoritesRow
                    analyticsRow
                }
                
                // About
                Section("关于") {
                    aboutRow
                    versionRow
                    feedbackRow
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .onAppear {
                calculateCacheSize()
            }
            .alert("清除缓存", isPresented: $showingClearCacheAlert) {
                Button("取消", role: .cancel) { }
                Button("清除", role: .destructive) {
                    clearCache()
                }
            } message: {
                Text("确定要清除所有缓存数据吗？这将删除已下载的图片和视频缓存。")
            }
            .alert("清除历史", isPresented: $showingClearHistoryAlert) {
                Button("取消", role: .cancel) { }
                Button("清除", role: .destructive) {
                    clearHistory()
                }
            } message: {
                Text("确定要清除所有观看历史吗？此操作无法撤销。")
            }
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
        }
    }
    
    // MARK: - Player Settings Rows
    private var playerTypeRow: some View {
        NavigationLink(destination: PlayerTypeSelectionView()) {
            HStack {
                Image(systemName: "play.circle")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("播放器类型")
                
                Spacer()
                
                Text(userPreferences.playerType.displayName)
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var autoplayRow: some View {
        HStack {
            Image(systemName: "play.fill")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("自动播放")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.autoPlay)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    private var backgroundPlayRow: some View {
        HStack {
            Image(systemName: "speaker.wave.2")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("后台播放")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.backgroundPlay)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    private var hardwareDecodingRow: some View {
        HStack {
            Image(systemName: "cpu")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("硬件解码")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.hardwareDecoding)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    // MARK: - Display Settings Rows
    private var themeRow: some View {
        NavigationLink(destination: ThemeSelectionView()) {
            HStack {
                Image(systemName: "paintbrush")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("主题")
                
                Spacer()
                
                Text(userPreferences.theme.themeDisplayName)
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var gridColumnsRow: some View {
        NavigationLink(destination: GridColumnsSelectionView()) {
            HStack {
                Image(systemName: "grid")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("网格列数")
                
                Spacer()
                
                Text("\(userPreferences.gridColumns) 列")
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var showThumbnailsRow: some View {
        HStack {
            Image(systemName: "photo")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("显示缩略图")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.showThumbnails)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    private var landscapeOrientationRow: some View {
        HStack {
            Image(systemName: "rotate.right")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("横屏播放")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.landscapeOrientation)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    // MARK: - Network Settings Rows
    private var timeoutRow: some View {
        NavigationLink(destination: TimeoutSelectionView()) {
            HStack {
                Image(systemName: "clock")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("请求超时")
                
                Spacer()
                
                Text("\(Int(userPreferences.requestTimeout)) 秒")
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var maxConcurrentRow: some View {
        NavigationLink(destination: ConcurrentSelectionView()) {
            HStack {
                Image(systemName: "arrow.down.circle")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("最大并发数")
                
                Spacer()
                
                Text("\(userPreferences.maxConcurrentDownloads)")
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var useWiFiOnlyRow: some View {
        HStack {
            Image(systemName: "wifi")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("仅WiFi播放")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.useWiFiOnly)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    // MARK: - Storage Settings Rows
    private var cacheSizeRow: some View {
        HStack {
            Image(systemName: "internaldrive")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("缓存大小")
            
            Spacer()
            
            Text(cacheSize)
                .foregroundColor(.secondaryText)
        }
    }
    
    private var clearCacheRow: some View {
        Button {
            showingClearCacheAlert = true
        } label: {
            HStack {
                Image(systemName: "trash")
                    .foregroundColor(.red)
                    .frame(width: 24)
                
                Text("清除缓存")
                    .foregroundColor(.red)
                
                Spacer()
            }
        }
    }
    
    private var clearHistoryRow: some View {
        Button {
            showingClearHistoryAlert = true
        } label: {
            HStack {
                Image(systemName: "clock.arrow.circlepath")
                    .foregroundColor(.red)
                    .frame(width: 24)
                
                Text("清除历史")
                    .foregroundColor(.red)
                
                Spacer()
            }
        }
    }
    
    private var maxCacheSizeRow: some View {
        NavigationLink(destination: CacheSizeSelectionView()) {
            HStack {
                Image(systemName: "externaldrive")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("最大缓存")
                
                Spacer()
                
                Text("\(Int(userPreferences.maxCacheSize / 1024 / 1024)) MB")
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    // MARK: - Privacy Settings Rows
    private var saveHistoryRow: some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("保存观看历史")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.saveHistory)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    private var saveFavoritesRow: some View {
        HStack {
            Image(systemName: "heart")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("保存收藏")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.saveFavorites)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    private var analyticsRow: some View {
        HStack {
            Image(systemName: "chart.bar")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("使用统计")
            
            Spacer()
            
            Toggle("", isOn: $userPreferences.enableAnalytics)
                .toggleStyle(SwitchToggleStyle(tint: .accent))
        }
    }
    
    // MARK: - About Rows
    private var aboutRow: some View {
        Button {
            showingAbout = true
        } label: {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("关于应用")
                    .foregroundColor(.primaryText)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    private var versionRow: some View {
        HStack {
            Image(systemName: "app.badge")
                .foregroundColor(.accent)
                .frame(width: 24)
            
            Text("版本")
            
            Spacer()
            
            Text(AppConstants.App.version)
                .foregroundColor(.secondaryText)
        }
    }
    
    private var feedbackRow: some View {
        Button {
            // Open feedback
        } label: {
            HStack {
                Image(systemName: "envelope")
                    .foregroundColor(.accent)
                    .frame(width: 24)
                
                Text("意见反馈")
                    .foregroundColor(.primaryText)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }
        }
    }
    
    // MARK: - Private Methods
    private func calculateCacheSize() {
        let size = storageService.getStorageSize()
        cacheSize = ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
    }
    
    private func clearCache() {
        CacheManager.shared.clearAllCache { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self?.calculateCacheSize()
                case .failure(let error):
                    print("Failed to clear cache: \(error)")
                }
            }
        }
    }
    
    private func clearHistory() {
        HistoryService.shared.clearHistory { result in
            switch result {
            case .success:
                print("History cleared successfully")
            case .failure(let error):
                print("Failed to clear history: \(error)")
            }
        }
    }
}

// MARK: - Selection Views
struct PlayerTypeSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        List {
            ForEach(PlayerType.allCases, id: \.self) { type in
                Button {
                    userPreferences.playerType = type
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text(type.displayName)
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.playerType == type {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("播放器类型")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ThemeSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    enum Theme: String, CaseIterable {
        case system = "system"
        case light = "light"
        case dark = "dark"
        
        var themeDisplayName: String {
            switch self {
            case .system: return "跟随系统"
            case .light: return "浅色"
            case .dark: return "深色"
            }
        }
    }
    
    var body: some View {
        List {
            ForEach(Theme.allCases, id: \.self) { theme in
                Button {
                    userPreferences.theme = theme.rawValue
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text(theme.themeDisplayName)
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.theme == theme.rawValue {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("主题")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct GridColumnsSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    let columns = [1, 2, 3, 4]
    
    var body: some View {
        List {
            ForEach(columns, id: \.self) { column in
                Button {
                    userPreferences.gridColumns = column
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text("\(column) 列")
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.gridColumns == column {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("网格列数")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct TimeoutSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    let timeouts: [TimeInterval] = [10, 15, 30, 60, 120]
    
    var body: some View {
        List {
            ForEach(timeouts, id: \.self) { timeout in
                Button {
                    userPreferences.requestTimeout = timeout
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text("\(Int(timeout)) 秒")
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.requestTimeout == timeout {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("请求超时")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ConcurrentSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    let concurrents = [1, 2, 3, 4, 5]
    
    var body: some View {
        List {
            ForEach(concurrents, id: \.self) { concurrent in
                Button {
                    userPreferences.maxConcurrentDownloads = concurrent
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text("\(concurrent)")
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.maxConcurrentDownloads == concurrent {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("最大并发数")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct CacheSizeSelectionView: View {
    @StateObject private var userPreferences = UserPreferences.shared
    @Environment(\.presentationMode) var presentationMode
    
    let cacheSizes: [Int64] = [100, 200, 500, 1000, 2000] // MB
    
    var body: some View {
        List {
            ForEach(cacheSizes, id: \.self) { size in
                Button {
                    userPreferences.maxCacheSize = size * 1024 * 1024 // Convert to bytes
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    HStack {
                        Text("\(size) MB")
                            .foregroundColor(.primaryText)
                        
                        Spacer()
                        
                        if userPreferences.maxCacheSize == size * 1024 * 1024 {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accent)
                        }
                    }
                }
            }
        }
        .navigationTitle("最大缓存")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - About View
struct AboutView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // App Icon
                    Image(systemName: "tv")
                        .font(.system(size: 80))
                        .foregroundColor(.accent)
                    
                    // App Info
                    VStack(spacing: 8) {
                        Text(AppConstants.App.name)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.primaryText)
                        
                        Text("版本 \(AppConstants.App.version)")
                            .font(.subheadline)
                            .foregroundColor(.secondaryText)
                    }
                    
                    // Description
                    Text("一个简洁、高效的免费电视播放器应用，支持多种视频源和播放格式。")
                        .font(.body)
                        .foregroundColor(.primaryText)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                    
                    // Features
                    VStack(alignment: .leading, spacing: 12) {
                        Text("主要功能")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primaryText)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            featureRow("多源支持", "支持多种视频源格式")
                            featureRow("高清播放", "支持高清视频播放")
                            featureRow("离线缓存", "支持视频离线缓存")
                            featureRow("历史记录", "自动保存观看历史")
                            featureRow("收藏功能", "收藏喜爱的视频")
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    Spacer()
                    
                    // Copyright
                    Text("© 2024 FreeTVPlayer. All rights reserved.")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
                .padding(.vertical, 20)
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    private func featureRow(_ title: String, _ description: String) -> some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.accent)
                .font(.system(size: 16))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primaryText)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondaryText)
            }
            
            Spacer()
        }
    }
}

// MARK: - Extensions

extension String {
    var themeDisplayName: String {
        switch self {
        case "system": return "跟随系统"
        case "light": return "浅色"
        case "dark": return "深色"
        default: return "跟随系统"
        }
    }
}

// MARK: - Preview
struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView()
            .preferredColorScheme(.dark)
    }
}