//
//  SearchView.swift
//  FreeTVPlayer
//
//  Created by Dev<PERSON>per on 2025/1/27.
//

import SwiftUI

struct SearchView: View {
    @ObservedObject var viewModel: SearchViewModel
    let dataSources: [DataSource]
    
    @Environment(\.presentationMode) var presentationMode
    @State private var searchText = ""
    @State private var selectedSource: DataSource?
    @FocusState private var isSearchFieldFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Header
                searchHeader
                
                // Source Selector
                if dataSources.count > 1 {
                    sourceSelector
                }
                
                // Content
                contentView
            }
            .navigationBarHidden(true)
            .onAppear {
                selectedSource = dataSources.first
                isSearchFieldFocused = true
            }
        }
    }
    
    // MARK: - Search Header
    private var searchHeader: some View {
        HStack(spacing: 12) {
            // Back Button
            Button(action: { presentationMode.wrappedValue.dismiss() }) {
                Image(systemName: "chevron.left")
                    .font(.title3)
                    .foregroundColor(.accent)
            }
            
            // Search Field
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondaryText)
                
                TextField("搜索视频...", text: $searchText)
                    .focused($isSearchFieldFocused)
                    .onSubmit {
                        performSearch()
                    }
                
                if !searchText.isEmpty {
                    Button(action: { 
                        searchText = ""
                        viewModel.clearResults()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondaryText)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.secondaryBackground)
            )
            
            // Search Button
            Button("搜索") {
                performSearch()
            }
            .foregroundColor(.accent)
            .disabled(searchText.trimmed.isEmpty)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }
    
    // MARK: - Source Selector
    private var sourceSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(dataSources.filter { $0.searchable }, id: \.key) { source in
                    Button(source.name) {
                        selectedSource = source
                        if !searchText.trimmed.isEmpty {
                            performSearch()
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(selectedSource?.key == source.key ? Color.accent : Color.secondaryBackground)
                    )
                    .foregroundColor(selectedSource?.key == source.key ? .white : .primaryText)
                    .font(.subheadline)
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Content View
    private var contentView: some View {
        Group {
            if viewModel.isLoading {
                loadingView
            } else if viewModel.searchResults.isEmpty && !viewModel.searchHistory.isEmpty && searchText.isEmpty {
                searchHistoryView
            } else if viewModel.searchResults.isEmpty && !searchText.isEmpty {
                emptyResultsView
            } else if !viewModel.searchResults.isEmpty {
                searchResultsView
            } else {
                emptyStateView
            }
        }
        .errorAlert(error: $viewModel.error)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .accent))
                .scaleEffect(1.2)
            
            Text("搜索中...")
                .font(.subheadline)
                .foregroundColor(.secondaryText)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "magnifyingglass")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("搜索视频内容")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("输入关键词搜索您想观看的视频")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Search History View
    private var searchHistoryView: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("搜索历史")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Spacer()
                
                Button("清空") {
                    viewModel.clearSearchHistory()
                }
                .font(.subheadline)
                .foregroundColor(.accent)
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(viewModel.searchHistory, id: \.self) { query in
                        HStack {
                            Image(systemName: "clock")
                                .font(.subheadline)
                                .foregroundColor(.secondaryText)
                            
                            Text(query)
                                .font(.subheadline)
                                .foregroundColor(.primaryText)
                            
                            Spacer()
                            
                            Button(action: {
                                viewModel.removeFromHistory(query)
                            }) {
                                Image(systemName: "xmark")
                                    .font(.caption)
                                    .foregroundColor(.secondaryText)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(Color.clear)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            searchText = query
                            performSearch()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Empty Results View
    private var emptyResultsView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "exclamationmark.magnifyingglass")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("未找到相关内容")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("尝试使用其他关键词或切换数据源")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Button("重新搜索") {
                performSearch()
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Search Results View
    private var searchResultsView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.searchResults, id: \.id) { video in
                    NavigationLink(destination: VideoDetailView(video: video, source: selectedSource!)) {
                        LargeVideoCardView(video: video)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // Load More Button
                if viewModel.hasMoreResults {
                    Button("加载更多") {
                        loadMoreResults()
                    }
                    .buttonStyle(SecondaryButtonStyle())
                    .padding(.top, 16)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 40)
        }
        .refreshable {
            performSearch()
        }
    }
    
    // MARK: - Private Methods
    private func performSearch() {
        guard !searchText.trimmed.isEmpty,
              let source = selectedSource else { return }
        
        isSearchFieldFocused = false
        viewModel.search(query: searchText.trimmed, in: source)
    }
    
    private func loadMoreResults() {
        guard let source = selectedSource else { return }
        viewModel.loadMoreResults(in: source)
    }
}

// MARK: - Search Suggestions View
struct SearchSuggestionsView: View {
    let suggestions: [String]
    let onSelect: (String) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ForEach(suggestions, id: \.self) { suggestion in
                HStack {
                    Image(systemName: "magnifyingglass")
                        .font(.subheadline)
                        .foregroundColor(.secondaryText)
                    
                    Text(suggestion)
                        .font(.subheadline)
                        .foregroundColor(.primaryText)
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.secondaryBackground)
                .contentShape(Rectangle())
                .onTapGesture {
                    onSelect(suggestion)
                }
                
                if suggestion != suggestions.last {
                    Divider()
                        .padding(.leading, 52)
                }
            }
        }
        .background(Color.secondaryBackground)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
}

// MARK: - Search Filter View
struct SearchFilterView: View {
    @Binding var selectedType: String?
    @Binding var selectedYear: String?
    @Binding var selectedRegion: String?
    
    let types = ["电影", "电视剧", "综艺", "动漫", "纪录片"]
    let years = ["2024", "2023", "2022", "2021", "2020"]
    let regions = ["大陆", "香港", "台湾", "美国", "日本", "韩国"]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Type Filter
            filterSection(title: "类型", options: types, selection: $selectedType)
            
            // Year Filter
            filterSection(title: "年份", options: years, selection: $selectedYear)
            
            // Region Filter
            filterSection(title: "地区", options: regions, selection: $selectedRegion)
        }
        .padding(20)
    }
    
    private func filterSection(title: String, options: [String], selection: Binding<String?>) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primaryText)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    // All option
                    Button("全部") {
                        selection.wrappedValue = nil
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(selection.wrappedValue == nil ? Color.accent : Color.secondaryBackground)
                    )
                    .foregroundColor(selection.wrappedValue == nil ? .white : .primaryText)
                    .font(.subheadline)
                    
                    ForEach(options, id: \.self) { option in
                        Button(option) {
                            selection.wrappedValue = option
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(selection.wrappedValue == option ? Color.accent : Color.secondaryBackground)
                        )
                        .foregroundColor(selection.wrappedValue == option ? .white : .primaryText)
                        .font(.subheadline)
                    }
                }
                .padding(.horizontal, 1)
            }
        }
    }
}

// MARK: - Preview
struct SearchView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleSources = [
            DataSource(key: "1", name: "示例源1", api: "https://example1.com", type: .json, searchable: true),
            DataSource(key: "2", name: "示例源2", api: "https://example2.com", type: .json, searchable: true)
        ]
        
        SearchView(
            viewModel: SearchViewModel(),
            dataSources: sampleSources
        )
        .preferredColorScheme(.dark)
    }
}