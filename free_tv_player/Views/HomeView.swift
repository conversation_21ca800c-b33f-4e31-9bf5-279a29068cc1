//
//  HomeView.swift
//  FreeTVPlayer
//
//  Created by Dev<PERSON>per on 2025/1/27.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var dataSourceViewModel = DataSourceViewModel()
    @StateObject private var videoListViewModel = VideoListViewModel()
    @StateObject private var searchViewModel = SearchViewModel()
    @State private var selectedTab = 0
    @State private var showingConfigurationSheet = false
    @State private var showingSearchSheet = false
    @State private var showingSettings = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Content
                if dataSourceViewModel.configuration.sources.isEmpty {
                    emptyStateView
                } else {
                    contentView
                }
            }
            .navigationBarHidden(true)
            .sheet(isPresented: $showingConfigurationSheet) {
                ConfigurationView(viewModel: dataSourceViewModel)
            }
            .sheet(isPresented: $showingSearchSheet) {
                SearchView(viewModel: searchViewModel, dataSources: dataSourceViewModel.validSources)
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView()
            }
            .errorAlert(error: $dataSourceViewModel.error)
            .errorAlert(error: $videoListViewModel.error)
        }
        .onFirstAppear {
            loadInitialData()
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            // App Title
            VStack(alignment: .leading, spacing: 4) {
                Text("FreeTVPlayer")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primaryText)
                
                if !dataSourceViewModel.validSources.isEmpty {
                    Text("\(dataSourceViewModel.sourceCount) 个数据源")
                        .font(.caption)
                        .foregroundColor(.secondaryText)
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 16) {
                // Search Button
                Button(action: { showingSearchSheet = true }) {
                    Image(systemName: "magnifyingglass")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
                .disabled(dataSourceViewModel.validSources.isEmpty)
                
                // Configuration Button
                Button(action: { showingConfigurationSheet = true }) {
                    Image(systemName: "gear")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
                
                // Settings Button
                Button(action: { showingSettings = true }) {
                    Image(systemName: "gearshape")
                        .font(.title3)
                        .foregroundColor(.accent)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color.primaryBackground)
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "tv")
                .font(.system(size: 64))
                .foregroundColor(.secondaryText)
            
            VStack(spacing: 8) {
                Text("欢迎使用 FreeTVPlayer")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primaryText)
                
                Text("请先配置数据源以开始使用")
                    .font(.body)
                    .foregroundColor(.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Button("配置数据源") {
                showingConfigurationSheet = true
            }
            .buttonStyle(PrimaryButtonStyle())
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Content View
    private var contentView: some View {
        TabView(selection: $selectedTab) {
            // Video List Tab
            VideoListView(viewModel: videoListViewModel, dataSources: dataSourceViewModel.validSources)
                .tabItem {
                    Image(systemName: "play.rectangle")
                    Text("视频")
                }
                .tag(0)
            
            // Live TV Tab
            if dataSourceViewModel.hasLiveChannels {
                LiveChannelView(configuration: dataSourceViewModel.configuration)
                    .tabItem {
                        Image(systemName: "tv")
                        Text("直播")
                    }
                    .tag(1)
            }
            
            // History Tab
            HistoryView()
                .tabItem {
                    Image(systemName: "clock")
                    Text("历史")
                }
                .tag(2)
            
            // Favorites Tab
            FavoritesView()
                .tabItem {
                    Image(systemName: "heart")
                    Text("收藏")
                }
                .tag(3)
        }
        .accentColor(.accent)
    }
    
    // MARK: - Private Methods
    private func loadInitialData() {
        // Load saved configuration first
        if !dataSourceViewModel.validSources.isEmpty {
            loadVideosFromFirstSource()
        }
    }
    
    private func loadVideosFromFirstSource() {
        guard let firstSource = dataSourceViewModel.validSources.first else { return }
        videoListViewModel.loadVideos(from: firstSource)
        videoListViewModel.loadCategories(from: firstSource)
    }
}

// MARK: - Video List View
struct VideoListView: View {
    @ObservedObject var viewModel: VideoListViewModel
    let dataSources: [DataSource]
    
    @State private var selectedSource: DataSource?
    @State private var showingSourcePicker = false
    @State private var showingFilterSheet = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Source Selector
            if dataSources.count > 1 {
                sourceSelector
            }
            
            // Category Filter
            if !viewModel.topLevelCategories.isEmpty {
                categoryFilter
            }
            
            // Video Grid
            videoGrid
        }
        .onAppear {
            if selectedSource == nil {
                selectedSource = dataSources.first
                loadVideos()
            }
        }
        .sheet(isPresented: $showingFilterSheet) {
            FilterView(currentFilter: viewModel.currentFilter) { filter in
                viewModel.applyFilter(filter)
            }
        }
    }
    
    // MARK: - Source Selector
    private var sourceSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(dataSources, id: \.key) { source in
                    Button(source.name) {
                        selectedSource = source
                        loadVideos()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(selectedSource?.key == source.key ? Color.accent : Color.secondaryBackground)
                    )
                    .foregroundColor(selectedSource?.key == source.key ? .white : .primaryText)
                    .font(.subheadline)
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 12)
    }
    
    // MARK: - Category Filter
    private var categoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                Button("全部") {
                    viewModel.selectCategory(nil)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(viewModel.selectedCategory == nil ? Color.accent : Color.secondaryBackground)
                )
                .foregroundColor(viewModel.selectedCategory == nil ? .white : .primaryText)
                .font(.subheadline)
                
                ForEach(viewModel.topLevelCategories, id: \.id) { category in
                    Button(category.name) {
                        viewModel.selectCategory(category)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(viewModel.selectedCategory?.id == category.id ? Color.accent : Color.secondaryBackground)
                    )
                    .foregroundColor(viewModel.selectedCategory?.id == category.id ? .white : .primaryText)
                    .font(.subheadline)
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Video Grid
    private var videoGrid: some View {
        ScrollView {
            if viewModel.isLoading && viewModel.videos.isEmpty {
                ProgressView("加载中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding(.top, 100)
            } else if viewModel.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "tv.slash")
                        .font(.system(size: 48))
                        .foregroundColor(.secondaryText)
                    
                    Text("暂无视频内容")
                        .font(.headline)
                        .foregroundColor(.secondaryText)
                    
                    Button("重新加载") {
                        viewModel.refreshVideos()
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
                .padding(.top, 100)
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    ForEach(viewModel.videos, id: \.id) { video in
                        NavigationLink(destination: VideoDetailView(video: video, source: selectedSource!)) {
                            VideoCardView(video: video)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .onAppear {
                            // Load more when reaching the end
                            if video.id == viewModel.videos.last?.id {
                                viewModel.loadMoreVideos()
                            }
                        }
                    }
                    
                    // Loading more indicator
                    if viewModel.isLoadingMore {
                        ProgressView()
                            .frame(height: 50)
                            .gridCellColumns(2)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
        }
        .refreshable {
            viewModel.refreshVideos()
        }
    }
    
    // MARK: - Private Methods
    private func loadVideos() {
        guard let source = selectedSource else { return }
        viewModel.loadVideos(from: source)
        viewModel.loadCategories(from: source)
    }
}

// MARK: - Preview
struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
        HomeView()
            .preferredColorScheme(.dark)
    }
}